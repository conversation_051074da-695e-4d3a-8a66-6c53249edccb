import { ref, computed, watch } from 'vue'
import { PreviewType } from '@jtypes'
import { FileTypeService } from '../services/FileTypeService'
import { getOutsideChain } from '@/api/common/common'
import JPGlobal from '@/types/common/jglobal'

/**
 * 预览文件接口
 */
export interface PreviewFile {
  /** 文件ID */
  id?: string
  /** 预览URL */
  url: string
  /** 文件类型 */
  type: PreviewType
  /** 文件名 */
  name?: string
  /** 文件二进制数据 */
  blob?: Blob
  /** 对象URL */
  objectUrl?: string
  /** 原始URL，用于Excel等特殊文件类型 */
  originalUrl?: string
}

interface InitOptions {
  url?: string
  ossPath?: string
  ossPathName?: string
  bucket?: string
  type?: string
  file?: File | File[] // 添加file属性，支持File对象或File数组
}

/**
 * 预览组件状态管理的组合式函数
 * @description 管理预览组件的所有响应式状态
 */
export function usePreviewState() {
  const files = ref<PreviewFile[]>([])
  const currentIndex = ref(0)
  const loading = ref(false)
  const initialized = ref(false)

  const currentFile = computed(() => files.value[currentIndex.value])
  const totalFiles = computed(() => files.value.length)

  const fileTypeService = new FileTypeService()

  /**
   * 处理文件URL
   * @param url 原始URL
   * @param type 文件类型
   */
  const processFileUrl = async (url: string, fileType: PreviewType): Promise<string> => {
    console.log('处理文件URL:', { url, fileType })
    // 对于PDF文件，我们需要获取blob URL以支持跨域访问
    if (fileType === PreviewType.PDF) {
      try {
        const response = await fetch(url, {
          mode: 'cors',
          credentials: 'include',
        })
        const blob = await response.blob()
        const blobUrl = URL.createObjectURL(blob)
        console.log('创建PDF blob URL:', { originalUrl: url, blobUrl })
        return blobUrl
      } catch (error) {
        console.error('获取PDF文件失败:', error)
        return url
      }
    }
    return url
  }

  /**
   * 获取OSS文件的外链URL
   */
  const getOSSUrl = async (path: string, bucket: string): Promise<string> => {
    try {
      console.log('获取OSS外链:', { path, bucket })
      const res = await getOutsideChain({ path, bucket })
      console.log('OSS外链获取成功:', { path, url: res.data })

      return JPGlobal.getRealOCUrl(res.data)
    } catch (error) {
      console.error('获取OSS外链失败:', { path, error })
      throw error
    }
  }

  /**
   * 获取文件类型
   */
  const getFileType = (url: string, specifiedType?: string): PreviewType => {
    if (specifiedType) {
      console.log('使用指定的文件类型:', { url, type: specifiedType })
      return specifiedType as PreviewType
    }
    const detectedType = fileTypeService.getFileType(url)
    console.log('检测文件类型:', { url, detectedType })
    return detectedType
  }

  /**
   * 从File对象创建预览文件
   * @param file File对象
   */
  const createFileFromObject = async (file: File): Promise<PreviewFile> => {
    const objectUrl = URL.createObjectURL(file)
    const fileType = getFileType(file.name)
    return {
      url: objectUrl,
      type: fileType,
      name: file.name,
      blob: file,
      objectUrl,
    }
  }

  const initializeFiles = async (options: InitOptions) => {
    try {
      if (initialized.value) {
        console.log('文件已初始化，跳过')
        return
      }

      console.log('开始初始化文件:', options)
      loading.value = true
      initialized.value = true

      if (options.file) {
        // 处理本地File对象
        console.log('处理本地File对象:', options.file)

        if (Array.isArray(options.file)) {
          // 处理File数组
          const processedFiles = await Promise.all(options.file.map(async file => createFileFromObject(file)))
          files.value = processedFiles
          console.log('多个File对象初始化完成:', files.value)
        } else {
          // 处理单个File对象
          const processedFile = await createFileFromObject(options.file)
          files.value = [processedFile]
          console.log('单个File对象初始化完成:', files.value[0])
        }
      } else if (options.url) {
        // 单文件预览
        const fileType = getFileType(options.url, options.type)
        const processedUrl = await processFileUrl(options.url, fileType)

        files.value = [
          {
            url: processedUrl,
            type: fileType,
            name: options.ossPathName,
            originalUrl: options.url,
          },
        ]
        console.log('单文件初始化完成:', files.value[0])
      } else if (options.ossPath && options.bucket) {
        // OSS文件预览
        const paths = options.ossPath.split(',').filter(Boolean)
        const names = options.ossPathName?.split(',').filter(Boolean) || []

        console.log('开始处理多文件:', { paths, names })
        // 获取OSS文件的外链URL
        const urls = await Promise.all(paths.map(path => getOSSUrl(path, options.bucket!)))

        // 处理每个文件的URL
        const processedFiles = await Promise.all(
          urls.map(async (url, index) => {
            const fileType = getFileType(url)
            const processedUrl = await processFileUrl(url, fileType)
            const file = {
              url: processedUrl,
              type: fileType,
              name: names[index] || `文件${index + 1} `,
              originalUrl: url,
            }
            console.log('文件处理完成:', { index, file })
            return file
          })
        )

        files.value = processedFiles
        console.log('多文件初始化完成:', files.value)
      }

      currentIndex.value = 0
    } catch (error) {
      console.error('初始化预览文件失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const setCurrentIndex = (index: number) => {
    if (index >= 0 && index < files.value.length) {
      currentIndex.value = index
    }
  }

  const cleanup = () => {
    console.log('开始清理资源')
    files.value.forEach(file => {
      if (file.objectUrl) {
        URL.revokeObjectURL(file.objectUrl)
      }
      // 如果URL是blob URL，也需要清理
      if (file.url.startsWith('blob:')) {
        URL.revokeObjectURL(file.url)
      }
    })
    files.value = []
    currentIndex.value = 0
    initialized.value = false
    console.log('资源清理完成')
  }

  // 监听文件列表变化，确保当前索引有效
  watch(files, newFiles => {
    if (currentIndex.value >= newFiles.length) {
      currentIndex.value = Math.max(0, newFiles.length - 1)
    }
  })

  return {
    files,
    currentIndex,
    currentFile,
    totalFiles,
    loading,
    initializeFiles,
    setCurrentIndex,
    cleanup,
  }
}
