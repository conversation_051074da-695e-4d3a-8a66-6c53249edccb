<!-- 
  @description 通用文件预览组件
  @example

  使用OSS路径预览文件
<preview :url="fileUrl" :type="fileType" :oss-path="ossPath" :oss-path-name="ossPathName" :bucket="bucket" />

直接使用File对象预览
<preview :file="fileObject" :show="dialogVisible" @update:show="dialogVisible = $event" />

预览多个File对象
<preview :file="fileArray" :show="dialogVisible" :show-pagination="true" @update:show="dialogVisible = $event" />
 -->

<template>
  <el-dialog
    v-model="dialogVisible"
    width="85%"
    top="1vh"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="preview-dialog"
    :show-close="false"
    :fullscreen="isFullscreen"
  >
    <div class="preview-wrapper">
      <div class="preview-toolbar">
        <div class="file-info">
          <span>文件：{{ currentFile?.name }}</span>
        </div>
        <div class="actions">
          <template v-if="currentPlugin">
            <div v-for="group in actionGroups" :key="group.name" class="action-group">
              <div class="button-group">
                <el-button
                  v-for="action in group.actions"
                  :key="action.key"
                  class="control-button"
                  :title="action.title"
                  :disabled="action.disabled"
                  @click="handleAction(action)"
                >
                  <template v-if="action.render">
                    <component :is="action.render" />
                  </template>
                  <span v-else-if="action.svg">
                    <n-icon v-html="action.svg" />
                    <span>{{ action.title }}</span>
                  </span>
                </el-button>
              </div>
            </div>
          </template>
          <el-button v-if="canDownload" @click="handleDownload">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
              <polyline points="7 10 12 15 17 10" />
              <line x1="12" y1="15" x2="12" y2="3" />
            </svg>
            <span>下载</span>
          </el-button>
          <el-button v-if="canPrint" v-print="'.preview-content'">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <polyline points="6 9 6 2 18 2 18 9" />
              <path d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2" />
              <rect x="6" y="14" width="12" height="8" />
            </svg>
            <span>打印</span>
          </el-button>
          <el-button v-if="files.length > 1" @click="showFileList = !showFileList">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
              <polyline points="14 2 14 8 20 8" />
              <line x1="16" y1="13" x2="8" y2="13" />
              <line x1="16" y1="17" x2="8" y2="17" />
              <line x1="10" y1="9" x2="8" y2="9" />
            </svg>
            <span>文件列表</span>
          </el-button>
          <el-button @click="toggleFullscreen">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3" />
            </svg>
            <span>全屏</span>
          </el-button>
          <el-button @click="close" type="danger">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18" />
              <line x1="6" y1="6" x2="18" y2="18" />
            </svg>
            <span>关闭</span>
          </el-button>
        </div>
      </div>
      <div class="preview-main" :class="{ 'preview-main-full': isFullscreen, 'preview-main-normal': !isFullscreen }">
        <div class="preview-content">
          <preview-container
            v-if="currentFile"
            :file="currentFile"
            @error="handlePreviewError"
            @scale-change="handleScaleChange"
            @rotation-change="handleRotationChange"
            ref="previewContainerRef"
          />
        </div>
        <div v-if="showFileList && files.length > 1" class="file-list">
          <div class="file-list-header">
            <span>文件列表</span>
            <span class="file-count">{{ files.length }}个文件</span>
          </div>
          <div class="file-list-content">
            <div
              v-for="(file, index) in files"
              :key="file.url"
              class="file-item"
              :class="{ active: index === currentIndex }"
              @click="setCurrentIndex(index)"
            >
              <i :class="getFileIcon(file.type)" class="file-icon" />
              <div class="file-name">
                {{ `${index + 1} .${file.name}` }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="showPagination" class="preview-pagination">
        <el-pagination
          v-model:current-page="currentPage"
          :total="totalFiles"
          :page-size="1"
          layout="prev, pager, next"
          @current-change="handlePageChange"
        />
      </div>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
  import { ref, computed, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import PreviewContainer from './PreviewContainer.vue'
  import { usePreviewState } from './composables/usePreviewState'
  import { useFilePreview } from './composables/useFilePreview'
  import { PreviewType } from '@jtypes'
  import type { PreviewAction } from './plugins/PreviewPlugin'

  interface PreviewProps {
    /** 是否显示 */
    show?: boolean
    /** 是否全屏 */
    fullscreenModel?: boolean
    /** 文件url */
    url?: string
    /** 文件对象或文件对象数组 */
    file?: File | File[]
    /** 文件oss路径 */
    ossPath?: string
    /** 文件oss路径名称 */
    ossPathName?: string
    /** 文件桶 */
    bucket?: string
    /** 文件类型 */
    type?: string
    /** 是否显示分页 */
    showPagination?: boolean
  }

  const props = withDefaults(defineProps<PreviewProps>(), {
    show: false,
    fullscreenModel: true,
    url: '',
    file: undefined,
    ossPath: '',
    ossPathName: '',
    bucket: '',
    type: '',
    showPagination: false,
  })

  const emit = defineEmits(['update:show'])

  const { files, currentIndex, currentFile, totalFiles, initializeFiles, setCurrentIndex, cleanup } = usePreviewState()
  const { title, canDownload, canPrint, handleDownload, handlePrint } = useFilePreview(currentFile)

  const dialogVisible = computed({
    get: () => props.show,
    set: value => emit('update:show', value),
  })

  // 图片预览状态
  const scale = ref(1)
  const rotation = ref(0)
  const isFullscreen = ref(props.fullscreenModel)
  const previewContainerRef = ref()
  const showFileList = ref(true)
  const previewMainHeight = computed(() => {
    return isFullscreen.value ? 'calc(100vh - 80px)' : 'calc(100vh - 180px)'
  })

  const isImage = computed(() => currentFile.value?.type === PreviewType.IMAGE)

  const handleClose = () => {
    cleanup()
    dialogVisible.value = false
  }

  const handlePageChange = (page: number) => {
    setCurrentIndex(page - 1)
  }

  // 图片操作处理函数
  const handleZoom = (type: 'in' | 'out') => {
    const delta = type === 'in' ? 0.1 : -0.1
    const newScale = scale.value + delta
    if (newScale >= 0.5 && newScale <= 3) {
      scale.value = newScale
      previewContainerRef.value?.updateScale?.(newScale)
    }
  }

  const rotate = (direction: 'left' | 'right') => {
    const delta = direction === 'left' ? -90 : 90
    rotation.value = (rotation.value + delta) % 360
    previewContainerRef.value?.updateRotation?.(rotation.value)
  }

  const resetImage = () => {
    scale.value = 1
    rotation.value = 0
    previewContainerRef.value?.resetTransform?.()
  }

  const toggleFullscreen = () => {
    isFullscreen.value = !isFullscreen.value
    previewContainerRef.value?.toggleFullscreen?.(isFullscreen.value)
  }

  const handleScaleChange = (newScale: number) => {
    scale.value = newScale
  }

  const handleRotationChange = (newRotation: number) => {
    rotation.value = newRotation
  }

  // 获取文件图标
  const getFileIcon = (type: string) => {
    switch (type) {
      case PreviewType.PDF:
        return 'el-icon-document-pdf'
      case PreviewType.IMAGE:
        return 'el-icon-picture'
      case PreviewType.EXCEL:
        return 'el-icon-document-excel'
      default:
        return 'el-icon-document'
    }
  }

  watch(
    () => props.show,
    async newVal => {
      if (newVal) {
        try {
          await initializeFiles({
            url: props.url,
            file: props.file,
            ossPath: props.ossPath,
            ossPathName: props.ossPathName,
            bucket: props.bucket,
            type: props.type,
          })
        } catch (error) {
          ElMessage.error('预览初始化失败')
          dialogVisible.value = false
        }
      } else {
        cleanup()
      }
    },
    { immediate: true }
  )

  watch(currentIndex, newIndex => {
    if (totalFiles.value > 0) {
      console.log('newIndex', newIndex)
      currentPage.value = newIndex + 1
    }
  })

  const currentPage = ref(1)

  const showPagination = computed(() => totalFiles.value > 1 && props.showPagination)

  const handlePreviewError = (error: Error) => {
    ElMessage.error(error.message)
  }

  // 获取当前插件的操作按钮
  const currentPlugin = computed(() => previewContainerRef.value?.getCurrentPlugin?.())
  const currentActions = computed(() => currentPlugin.value?.getActions?.(currentFile.value) || [])

  // 按钮分组
  const actionGroups = computed(() => {
    const groups: { [key: string]: PreviewAction[] } = {}
    currentActions.value.forEach(action => {
      const groupName = action.group || 'default'
      if (!groups[groupName]) {
        groups[groupName] = []
      }
      groups[groupName].push(action)
    })
    return Object.entries(groups).map(([name, actions]) => ({
      name,
      actions: actions.sort((a, b) => (a.order || 0) - (b.order || 0)),
    }))
  })

  // 处理按钮点击
  const handleAction = (action: PreviewAction) => {
    if (action.disabled) return
    if (currentPlugin.value) {
      action.action(currentPlugin.value.instance, currentFile.value)
    }
  }

  const close = () => {
    dialogVisible.value = false
  }
</script>
<style lang="scss" scoped>
  .preview-dialog {
    :deep(.el-dialog) {
      margin: 0 !important;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      max-height: 98vh;
      max-width: 98vw;
      display: flex;
      flex-direction: column;
      border-radius: 8px;
      overflow: hidden;

      .el-dialog__header {
        margin: 0;
        padding: 12px 20px;
      }

      .el-dialog__body {
        padding: 0;
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        height: calc(100vh - 120px);
      }
    }

    .preview-wrapper {
      height: 100%;
      display: flex;
      flex-direction: column;

      .preview-toolbar {
        padding: 12px 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #eee;
        flex-shrink: 0;
        background: #fff;
        z-index: 10;

        .file-info {
          font-size: 14px;
          color: #666;
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .actions {
          display: flex;
          align-items: center;
          gap: 16px;

          .action-group {
            display: flex;
            align-items: center;
            gap: 16px;
            padding-right: 16px;
            border-right: 1px solid #eee;

            &:last-child {
              border-right: none;
            }

            .button-group {
              display: flex;
              gap: 4px;
              padding: 2px;
              background: #f5f7fa;
              border-radius: 4px;
            }

            .control-button {
              padding: 6px;
              height: 32px;
              border: none;
              background: transparent;
              display: flex;
              align-items: center;
              gap: 4px;

              svg {
                width: 16px;
                height: 16px;
              }

              &:hover:not(:disabled) {
                background: #e4e7ed;
              }

              &:disabled {
                opacity: 0.5;
                cursor: not-allowed;
              }
            }
          }

          .el-button {
            display: flex;
            align-items: center;
            gap: 4px;

            svg {
              width: 16px;
              height: 16px;
            }
          }
        }
      }

      .preview-main {
        height: 100%;
        background: rgba(0, 0, 0, 0.02);
        display: flex;
        flex-direction: row;
        overflow: hidden;

        .preview-content {
          height: 100%;
          flex: 1;
          min-width: 0;
          padding: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .file-list {
          width: 300px;
          border-left: 1px solid #eee;
          background: #fff;
          display: flex;
          flex-direction: column;
          flex-shrink: 0;

          .file-list-header {
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #eee;

            span {
              font-size: 14px;
              color: #666;
            }

            .file-count {
              font-size: 12px;
              color: #999;
              background: #f5f7fa;
              padding: 2px 8px;
              border-radius: 10px;
            }
          }

          .file-list-content {
            flex: 1;
            overflow-y: auto;
            padding: 8px;

            .file-item {
              padding: 8px 12px;
              display: flex;
              align-items: center;
              gap: 8px;
              cursor: pointer;
              border-radius: 4px;
              margin-bottom: 4px;

              &:hover {
                background: #f5f7fa;
              }

              &.active {
                background: #ecf5ff;
                color: #409eff;
              }

              .file-icon {
                font-size: 16px;
              }

              .file-name {
                flex: 1;
                font-size: 14px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }
        }
      }

      .preview-pagination {
        padding: 12px;
        display: flex;
        justify-content: center;
        border-top: 1px solid #eee;
        flex-shrink: 0;
        background: #fff;
      }
    }
  }
</style>

<style scoped>
  .preview-main-full {
    height: calc(100vh - 130px) !important;
  }

  .preview-main-normal {
    height: calc(100vh - 150px) !important;
  }
</style>
