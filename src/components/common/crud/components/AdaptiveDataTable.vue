<script lang="tsx">
  import { defineComponent, inject, ref, computed, PropType, Ref } from 'vue'
  import { NDataTable, NSpace, NButton, NIcon, NGrid, NGridItem, NTag, NText, NEmpty, NSpin, NTooltip } from 'naive-ui'
  import { CardOutline, ListOutline,TabletPortraitOutline, SettingsOutline, PhonePortraitOutline } from '@vicons/ionicons5'
  import {
    TableBaseColumn,
    TableColumn,
    TableColumnGroup,
    TableExpandColumn,
    TableSelectionColumn,
  } from 'naive-ui/lib/data-table/src/interface'

  // 扩展列配置接口，支持移动端属性
  type ExtendedColumn<T> = {
    // 移动端扩展属性
    mobileTitle?: boolean // 是否作为移动端主标题
    mobileSubtitle?: boolean // 是否作为移动端副标题
    mobileShow?: boolean // 移动端是否显示
    mobileOrder?: number // 移动端显示顺序
    mobilePosition?: 'header' | 'body' | 'footer' // 移动端显示位置
    mobileSpan?: number // 移动端占用列数
    realKey?: string // 真实的key字段
    hide?: boolean // 是否隐藏  列
  } & TableColumnGroup<T> &
    TableBaseColumn<T> &
    TableSelectionColumn<T> &
    TableExpandColumn<T>

  var test: ExtendedColumn<any>

  const AdaptiveDataTable = defineComponent({
    name: 'AdaptiveDataTable',
    extends: NDataTable,
    props: {
      // 重写colmun
      columns: {
        type: Array as PropType<ExtendedColumn<any>[]>,
        default: () => [],
      },
      // === 移动端专用属性 ===
      useMobileView: {
        type: Boolean,
        default: true,
      },
      showMobileHeader: {
        type: Boolean,
        default: true,
      },
      mobileTitle: {
        type: String,
        default: '移动端数据列表',
      },
      showViewToggle: {
        type: Boolean,
        default: true,
      },
      showMobileConfig: {
        type: Boolean,
        default: true,
      },
      cardColumns: {
        type: Number,
        default: 1,
      },
      showActions: {
        type: Boolean,
        default: false,
      },
    },
    emits: [],
    setup(props, { emit, slots }) {
      // 移动端检测
      const isMobileDevice = inject<Ref<Boolean>>('isMobileDevice')

      // 移动端视图状态
      const isCardView = ref(true) // 默认卡片视图
      const showMobileConfig = ref(false)

      // 处理后的列配置
      const processedColumns = computed(() => {
        if (!props.columns) return []

        // .map(col => {
        //                     if (!col.width) {
        //                       col.width = 150
        //                     }
        //                     return col
        //                   })
        //                   .filter(col => !col.hide) as any
        return props.columns.map((col, index) => ({
          ...col,
          // 设置默认值
          mobileShow: col.mobileShow !== false,
          mobileOrder: col.mobileOrder || index + 1,
          mobilePosition: col.mobilePosition || 'body',
          mobileSpan: col.mobileSpan || 1,
          width: col.width || 100,
        }))
      })

      // 移动端显示的列（按顺序排序）
      const mobileColumns = computed(() => {
        return processedColumns.value
          .filter(col => col.mobileShow !== false)
          .sort((a, b) => (a.mobileOrder || 0) - (b.mobileOrder || 0))
      })

      // 主标题列
      const titleColumn = computed(() => {
        return mobileColumns.value.find(col => col.mobileTitle) || mobileColumns.value[0]
      })

      // 副标题列
      const subtitleColumn = computed(() => {
        return mobileColumns.value.find(col => col.mobileSubtitle)
      })

      // 切换视图模式
      const toggleView = () => {
        isCardView.value = !isCardView.value
      }

      // 处理行点击事件
      const handleRowClick = (row: any, index: number) => {
        emit('row-click', row, index)
      }

      // 渲染单个卡片
      const renderCard = (row: any, index: number) => {
        // 根据列数调整卡片内边距
        const isFullWidth = props.cardColumns === 1
        const cardPadding = isFullWidth ? '10px' : '10px'
        const cardMinHeight = isFullWidth ? '140px' : '120px'

        const cardStyle = {
          background: '#ffffff',
          border: '1px solid #e5e7eb',
          borderRadius: '8px',
          padding: cardPadding,
          cursor: 'pointer',
          minHeight: cardMinHeight,
        }

        return (
          <div style={cardStyle} class="mobile-card" onClick={() => handleRowClick(row, index)}>
            {/* 卡片头部 */}
            <div class={`flex justify-between items-start ${isFullWidth ? 'mb-4' : 'mb-3'}`}>
              <div class="flex-1">
                {/* 主标题 */}
                {titleColumn.value && (
                  <div class={`font-semibold text-gray-900 ${isFullWidth ? 'text-lg mb-2' : 'text-base mb-1'}`}>
                    {titleColumn.value.render
                      ? titleColumn.value.render(row, index)
                      : row[titleColumn.value.key || titleColumn.value.realKey || '']}
                  </div>
                )}

                {/* 副标题 */}
                {subtitleColumn.value && (
                  <div class={`text-gray-600 ${isFullWidth ? 'text-base' : 'text-sm'}`}>
                    {subtitleColumn.value.render
                      ? subtitleColumn.value.render(row, index)
                      : row[subtitleColumn.value.key || subtitleColumn.value.realKey || '']}
                  </div>
                )}
              </div>

              {/* 头部位置的字段 */}
              {mobileColumns.value
                .filter(
                  col => col.mobilePosition === 'header' && col !== titleColumn.value && col !== subtitleColumn.value
                )
                .map(col => (
                  <div key={col.key} class="text-right">
                    {col.render ? (
                      col.render(row, index)
                    ) : (
                      <NTag size="small">{row[col.key || col.realKey || '']}</NTag>
                    )}
                  </div>
                ))}
            </div>

            {/* 卡片主体 */}
            <div class={isFullWidth ? 'space-y-3' : 'space-y-2'}>
              <NGrid cols={props.cardColumns} xGap={isFullWidth ? 16 : 12} yGap={isFullWidth ? 12 : 8}>
                {mobileColumns.value
                  .filter(
                    col => col.mobilePosition === 'body' && col !== titleColumn.value && col !== subtitleColumn.value
                  )
                  .map(col => (
                    <NGridItem key={col.key} span={col.mobileSpan || 1}>
                      <div class={`text-gray-500 ${isFullWidth ? 'text-sm mb-2' : 'text-xs mb-1'}`}>{col.title}</div>
                      <div class={`text-gray-900 ${isFullWidth ? 'text-base' : 'text-sm'}`}>
                        {col.render ? col.render(row, index) : row[col.key || col.realKey || ''] || '-'}
                      </div>
                    </NGridItem>
                  ))}
              </NGrid>
            </div>

            {/* 卡片底部 */}
            {mobileColumns.value.some(col => col.mobilePosition === 'footer') && (
              <div
                class={`flex justify-between items-center border-t border-gray-100 ${
                  isFullWidth ? 'mt-4 pt-4' : 'mt-3 pt-3'
                }`}
              >
                {mobileColumns.value
                  .filter(col => col.mobilePosition === 'footer')
                  .map(col => (
                    <div key={col.key} class={`text-gray-500 ${isFullWidth ? 'text-sm' : 'text-xs'}`}>
                      {col.render
                        ? col.render(row, index)
                        : `${col.title}: ${row[col.key || col.realKey || ''] || '-'}`}
                    </div>
                  ))}
              </div>
            )}

            {/* 操作按钮 */}
            {props.showActions && slots.actions && (
              <div class={`border-t border-gray-100 ${isFullWidth ? 'mt-4 pt-4' : 'mt-3 pt-3'}`}>
                {slots.actions({ row, index })}
              </div>
            )}
          </div>
        )
      }

      // 渲染移动端卡片网格
      const renderMobileCards = () => {
        if (!props.data || props.data.length === 0) {
          return (
            <div class="flex justify-center items-center py-16">
              <NEmpty description="暂无数据" />
            </div>
          )
        }

        // 根据列数调整间距和内边距
        const isFullWidth = props.cardColumns === 1
        const gridClass = isFullWidth ? 'px-2' : 'p-4'
        const xGap = isFullWidth ? 0 : 16
        const yGap = isFullWidth ? 8 : 16

        return (
          <NGrid cols={props.cardColumns} xGap={xGap} yGap={yGap} class={gridClass}>
            {props.data.map((row, index) => (
              <NGridItem
                key={typeof props.rowKey === 'function' ? props.rowKey(row) : row[props.rowKey as string] || index}
              >
                {renderCard(row, index)}
              </NGridItem>
            ))}
          </NGrid>
        )
      }

      // 渲染移动端头部
      const renderMobileHeader = () => {
        if (!props.showMobileHeader) return null

        return (
          <div class="flex justify-between items-center p-4 bg-white border-b border-gray-100">
            <div class="flex items-center space-x-3">
              <NIcon size={20} color="#6366f1">
                <PhonePortraitOutline />
              </NIcon>
              <NText class=" font-semibold text-gray-900">{props.mobileTitle}</NText>
              {props.data && (
                <NTag size="small" type="info">
                  {props.data.length} 条
                </NTag>
              )}
            </div>

            <NSpace size={8}>
              {/* 视图切换按钮 */}
              {props.showViewToggle && (
                <NTooltip trigger="hover">
                  {{
                    trigger: () => (
                      <NButton
                        size="small"
                        quaternary
                        circle
                        onClick={toggleView}
                        type={isCardView.value ? 'primary' : 'default'}
                      >
                        <NIcon size={16}>{isCardView.value ? <CardOutline /> : <ListOutline />}</NIcon>
                      </NButton>
                    ),
                    default: () => (isCardView.value ? '切换到表格视图' : '切换到卡片视图'),
                  }}
                </NTooltip>
              )}

              {/* 配置按钮 */}
              {props.showMobileConfig && (
                <NTooltip trigger="hover">
                  {{
                    trigger: () => (
                      <NButton
                        size="small"
                        quaternary
                        circle
                        onClick={() => (showMobileConfig.value = !showMobileConfig.value)}
                      >
                        <NIcon size={16}>
                          <SettingsOutline />
                        </NIcon>
                      </NButton>
                    ),
                    default: () => '移动端配置',
                  }}
                </NTooltip>
              )}
            </NSpace>
          </div>
        )
      }

      if (!isMobileDevice.value && props.useMobileView) {
        // 渲染原始DataTable

        return () => h(NDataTable, props, slots)
      } else {
        return () => {
          return (
            <div class="adaptive-data-table-mobile" data-columns={props.cardColumns}>
              {/* 移动端头部 */}
              {renderMobileHeader()}

              {/* 加载状态 */}
              {props.loading && (
                <div class="flex justify-center items-center py-16">
                  <NSpin size="large" description="加载中..." />
                </div>
              )}

              {/* 移动端内容 */}
              {!props.loading && (
                <div>
                  {isCardView.value ? (
                    // 卡片视图
                    renderMobileCards()
                  ) : (
                    // 表格视图（移动端优化）
                      <NDataTable
                        data={props.data}
                        columns={processedColumns.value}
                        loading={props.loading}
                        size="small"
                        bordered={false}
                        striped={true}
                        singleLine={false}
                        style={{ minWidth: '600px' }}
                        onUpdate:checkedRowKeys={(keys: any[]) => emit('update:checked-row-keys', keys)}
                        onUpdate:filters={(filters: any, sourceColumn: any) =>
                          emit('update:filters', filters, sourceColumn)
                        }
                        onUpdate:sorter={(sorter: any) => emit('update:sorter', sorter)}
                        onUpdate:page={(page: number) => emit('update:page', page)}
                        onUpdate:pageSize={(pageSize: number) => emit('update:page-size', pageSize)}
                        v-slots={slots}
                      />
                  )}
                </div>
              )}
            </div>
          )
        }
      }
    },
  })
  export default AdaptiveDataTable
</script>

<style scoped lang="less">
  // 自适应数据表格样式 - 无动画版本

  .adaptive-data-table-pc {
    /* PC端样式保持原有NaiveUI样式 */
    width: 100%;
  }

  // 单列布局优化
  .adaptive-data-table-mobile {
    // 单列布局时的特殊样式
    &[data-columns] {
      .mobile-card {
        margin: 0 8px 8px 8px;
        padding: 5px;
        min-height: 140px;

        // 更大的字体和间距
        .card-title {
          font-size: 1.125rem;
          margin-bottom: 8px;
        }

        .card-subtitle {
          font-size: 1rem;
          margin-bottom: 16px;
        }

        .card-content {
          .field-label {
            font-size: 0.875rem;
            margin-bottom: 8px;
          }

          .field-value {
            font-size: 1rem;
            margin-bottom: 12px;
          }
        }
      }
    }
  }

  // 响应式断点
  @media (max-width: 640px) {
    .adaptive-data-table-mobile {
      .mobile-card {
        margin: 8px;
        padding: 12px;
      }

      // 小屏幕下单列布局的调整
      &[data-columns] {
        .mobile-card {
          margin: 0 4px 8px 4px;
          padding: 0px;
        }
      }
    }
  }
</style>
