<template>
  <div
    ref="titleRef"
    class="title-line"
    :class="{
      'title-line-mobile': isMobileDevice,
      'title-line-pc': !isMobileDevice,
    }"
  >
    <div class="title-line-left" :class="{ 'with-sequence': isMobileDevice && showSequence }">
      {{ title }}
    </div>
    <div><slot /></div>
  </div>
</template>
<script lang="ts">
  import { defineComponent, inject, type Ref, ref } from 'vue'

  export default defineComponent({
    props: {
      // 标题
      title: {
        type: String,
        default: '标题',
      },
      // 是否显示序号（默认移动端显示）
      showSequence: {
        type: Boolean,
        default: true,
      },
      // 是否启用点击滚动（默认启用）
      enableClickScroll: {
        type: Boolean,
        default: true,
      },
      // 滚动偏移量（默认-80px，避免被固定头部遮挡）
      scrollOffset: {
        type: Number,
        default: -80,
      },
    },
    setup(props) {
      // 移动端检测
      const isMobileDevice = inject<Ref<Boolean>>('isMobileDevice')

      // 组件引用
      const titleRef = ref<HTMLElement>()

      return {
        isMobileDevice,
        titleRef,
      }
    },
  })
</script>
<style lang="less" scoped>
  @import url(@/assets/css/common/common.less);

  // CSS计数器样式
  :global(body) {
    counter-reset: title-sequence;
  }

  .title-line-pc {
    height: 1.2rem;
    margin-bottom: 0.8rem !important;
    width: 100% !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    font-weight: normal !important;
  }
  .title-line {


    &-left {
      height: 1.2rem;
      padding-left: 0.5rem !important;
      border-left: 0.3em solid var(--j-frame-color) !important;

      // 移动端显示序号
      &.with-sequence {
        counter-increment: title-sequence;

        &::before {
          content: counter(title-sequence) '. ';
          color: var(--j-frame-color) !important;
          font-weight: 600 !important;
          font-size: 1.1em !important;
          margin-right: 0.2rem !important;
        }
      }
    }

    // 移动端黏性定位样式
    &-mobile {
      position: sticky !important;
      top: 0 !important;
      z-index: 100 !important;
      // 毛玻璃背景效果
      background: rgba(255, 255, 255, 0.8) !important;
      backdrop-filter: blur(16px) saturate(180%) !important;
      -webkit-backdrop-filter: blur(16px) saturate(180%) !important;
      // 边框
      border-bottom: 1px solid rgba(255, 255, 255, 0.3) !important;
      // 布局
      margin-bottom: 1.2rem !important;
      padding: 0.2rem 0 !important;
      font-size: 16px !important;
      font-weight: 500 !important;

      &.clickable:hover {
        background: rgba(255, 255, 255, 0.66) !important;
        border-bottom-color: var(--j-frame-color) !important;
      }

      // 暗色主题适配
      :global([data-theme='dark']) & {
        background: rgba(16, 16, 20, 0.8) !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;

        &.clickable:hover {
          background: rgba(16, 16, 20, 0.9) !important;
        }
      }

      .title-line-left {
        font-size: 1.1em !important;
        line-height: 1.5 !important;
        height: auto !important;
        padding: 0.3rem 0.5rem !important;

        &.with-sequence::before {
          font-size: 18px !important;
          color: var(--j-frame-color) !important;
          font-weight: 700 !important;
          margin-right: 0.3rem !important;
        }
      }
    }
  }
</style>
