import { ref, watch, onMounted, onBeforeUnmount, type Ref } from 'vue'
import BpmnViewer from 'bpmn-js/lib/Viewer'
import DefaultEmptyXML from '../plugins/defaultEmpty'
import { useHighlightManager } from './useHighlightManager'
import { useInteractionController } from './useInteractionController'
import { useOverlayManager } from './useOverlayManager'
import { useMobileAdapter } from './useMobileAdapter'

/**
 * BPMN查看器主要组合式函数
 * 整合所有功能模块，提供统一的接口
 */
export function useBpmnViewer(
  isMobileDevice: Ref<Boolean>,
  props: any,
  emit: any
) {
  // 核心状态
  const bpmnCanvas = ref<HTMLElement>()
  let bpmnModeler: any = null
  const xml = ref('')
  const activityLists = ref<any[]>([])
  const processInstance = ref<any>(undefined)
  const taskList = ref<any[]>([])

  // 初始化各个功能模块
  const highlightManager = useHighlightManager()
  const interactionController = useInteractionController(isMobileDevice)
  const overlayManager = useOverlayManager()
  const mobileAdapter = useMobileAdapter(isMobileDevice)

  /**
   * 初始化BPMN建模器
   */
  const initBpmnModeler = () => {
    if (bpmnModeler || !bpmnCanvas.value) return

    const cfg = mobileAdapter.getBpmnConfig(bpmnCanvas.value)
    bpmnModeler = new BpmnViewer(cfg)

    // 设置建模器实例到交互控制器
    interactionController.setBpmnModeler(bpmnModeler)
    
    // 移动端特殊初始化
    mobileAdapter.handleMobileInit(bpmnModeler)
  }

  /**
   * 创建新的流程图
   * @param xmlString XML字符串
   */
  const createNewDiagram = async (xmlString: string) => {
    if (!bpmnModeler) return

    const newId = `Process_${new Date().getTime()}`
    const newName = `业务流程_${new Date().getTime()}`
    const finalXml = xmlString || DefaultEmptyXML(newId, newName, props.prefix)

    try {
      const { warnings } = await bpmnModeler.importXML(finalXml)
      if (warnings && warnings.length) {
        warnings.forEach((warn: any) => console.warn(warn))
      }

      // 高亮流程图
      await highlightManager.highlightDiagram(
        bpmnModeler,
        activityLists,
        taskList,
        processInstance,
        emit
      )

      // 移动端特殊处理
      if (isMobileDevice.value) {
        // 移动端延迟适应视口，确保内容完整显示
        setTimeout(() => {
          mobileAdapter.handleMobileInit(bpmnModeler)
        }, 300)
      } else {
        // 桌面端直接适应视口
        interactionController.fitViewport()
      }
    } catch (e) {
      console.error('BPMN导入失败:', e)
    }
  }

  /**
   * 初始化模型监听器
   */
  const initModelListeners = () => {
    if (!bpmnModeler) return

    const eventBus = bpmnModeler.get('eventBus')

    // 元素悬停事件
    eventBus.on('element.hover', (eventObj: any) => {
      const element = eventObj?.element
      if (element) {
        overlayManager.showElementOverlay(
          element,
          activityLists,
          taskList,
          processInstance,
          bpmnModeler
        )
      }
    })

    // 元素离开事件
    eventBus.on('element.out', (eventObj: any) => {
      const element = eventObj?.element
      if (element) {
        overlayManager.removeElementOverlay(element)
      }
    })

    // 画布缩放事件 - 重新计算overlay位置
    eventBus.on('canvas.viewbox.changed', () => {
      // 清除所有overlay，避免位置错乱
      overlayManager.clearAllOverlays()
    })

    // 画布大小变化事件
    eventBus.on('canvas.resized', () => {
      mobileAdapter.handleViewportChange(bpmnModeler)
    })
  }

  /**
   * 初始化所有功能
   */
  const init = () => {
    xml.value = props.value
    activityLists.value = props.activityData
    processInstance.value = props.processInstanceData
    taskList.value = props.taskData

    // 初始化建模器
    initBpmnModeler()
    
    if (bpmnModeler) {
      // 创建流程图
      createNewDiagram(xml.value)
      
      // 初始化监听器
      initModelListeners()
      // 
      // 初始化交互功能
      interactionController.initDragging()
      interactionController.initTouchZoom()
      
      // 初始化默认缩放
      interactionController.initDefaultZoom()
    }
  }

  /**
   * 重新初始化（用于响应式更新）
   */
  const reinit = () => {
    if (bpmnModeler) {
      bpmnModeler.destroy()
      bpmnModeler = null
    }
    
    // 清理覆盖层
    overlayManager.clearAllOverlays()
    
    // 重新初始化
    setTimeout(() => {
      init()
    }, 100)
  }

  /**
   * 销毁实例
   */
  const destroy = () => {
    if (bpmnModeler) {
      bpmnModeler.destroy()
      emit('destroy', bpmnModeler)
      bpmnModeler = null
    }
    
    // 清理覆盖层
    overlayManager.clearAllOverlays()
    
    // 清理交互控制器
    interactionController.cleanup()
  }

  // 监听移动端状态变化
  watch(isMobileDevice, () => {
    reinit()
  })

  // 监听props变化
  watch(
    () => props.value,
    (newValue) => {
      xml.value = newValue
      if (bpmnModeler) {
        createNewDiagram(xml.value)
      }
    }
  )

  watch(
    () => props.activityData,
    (newActivityData) => {
      activityLists.value = newActivityData
      if (bpmnModeler) {
        createNewDiagram(xml.value)
      }
    }
  )

  watch(
    () => props.processInstanceData,
    (newProcessInstanceData) => {
      processInstance.value = newProcessInstanceData
      if (bpmnModeler) {
        createNewDiagram(xml.value)
      }
    }
  )

  watch(
    () => props.taskData,
    (newTaskListData) => {
      taskList.value = newTaskListData
      if (bpmnModeler) {
        createNewDiagram(xml.value)
      }
    }
  )

  // 生命周期钩子
  onMounted(() => {
    init()
  })

  onBeforeUnmount(() => {
    destroy()
  })

  return {
    // 核心状态
    bpmnCanvas,
    xml,
    activityLists,
    processInstance,
    taskList,

    // 功能模块
    highlightManager,
    interactionController,
    overlayManager,
    mobileAdapter,

    // 核心方法
    init,
    reinit,
    destroy,
    createNewDiagram,
    initBpmnModeler,
    initModelListeners,

    // 获取建模器实例
    getBpmnModeler: () => bpmnModeler
  }
}
