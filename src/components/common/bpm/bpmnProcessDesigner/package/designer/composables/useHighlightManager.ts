import { ref, type Ref } from 'vue'
import { getIntDictOptions, DICT_TYPE } from '@/utils/bpmAdapter/bpmDictAdapter'
import { isEmpty } from '@/utils/bpmAdapter/is'

/**
 * BPMN节点高亮管理器
 * 负责处理流程图中节点的高亮显示逻辑
 */
export function useHighlightManager() {
  // 是否显示多签图例
  const showMultiSignLegend = ref(false)

  /**
   * 获取节点高亮样式类名
   * @param activity 活动数据
   * @returns CSS类名
   */
  const getActivityHighlightCss = (activity: any): string => {
    return activity.endTime ? 'highlight' : 'highlight-todo'
  }

  /**
   * 根据任务状态获取结果样式
   * @param status 任务状态
   * @returns CSS类名
   */
  const getResultCss = (status: number): string => {
    const statusMap: Record<number, string> = {
      0: 'highlight-todo',    // 待审批
      1: 'highlight-todo',    // 审批中
      2: 'highlight',         // 已通过
      3: 'highlight-reject',  // 不通过
      4: 'highlight-cancel',  // 已取消
      5: 'highlight-return',  // 退回
      6: 'highlight-todo',    // 委派
      7: 'highlight-todo',    // 审批通过中
    }
    return statusMap[status] || ''
  }

  /**
   * 判断多签类型并返回对应的CSS类名
   * @param element BPMN元素
   * @returns 多签类型CSS类名
   */
  const getMultiSignTypeClass = (element: any): string => {
    if (element.$type !== 'bpmn:UserTask' || !element.loopCharacteristics) {
      return ''
    }

    // 如果不是多实例循环特性，则不是多签
    if (element.loopCharacteristics.$type !== 'bpmn:MultiInstanceLoopCharacteristics') {
      return ''
    }

    // 是否串行执行
    const isSequential = element.loopCharacteristics.isSequential || false

    // 解析完成条件判断是会签还是或签
    const completionCondition = element.loopCharacteristics.completionCondition?.body || ''
    const isOrSign = completionCondition.includes('nrOfCompletedInstances > 0') // 只要有人完成即可
    const isAndSign = completionCondition.includes('>= nrOfInstances') // 需要所有人完成

    if (isSequential) {
      return 'multi-sequential' // 串行
    } else if (isOrSign) {
      return 'multi-or' // 或签
    } else if (isAndSign) {
      return 'multi-and' // 会签
    } else {
      return 'multi-parallel' // 并行
    }
  }

  /**
   * 获取活动的出线连接
   * @param activity 活动数据
   * @param bpmnModeler BPMN建模器实例
   * @returns 出线连接数组
   */
  const getActivityOutgoing = (activity: any, bpmnModeler: any): any[] => {
    // 如果有 outgoing，则直接使用它
    if (activity.outgoing && activity.outgoing.length > 0) {
      return activity.outgoing
    }

    // 如果没有，则遍历获得起点为它的【bpmn:SequenceFlow】节点们
    const flowElements = bpmnModeler.getDefinitions().rootElements[0].flowElements
    const outgoing: any[] = []
    flowElements?.forEach((item: any) => {
      if (item.$type !== 'bpmn:SequenceFlow') {
        return
      }
      if (item.sourceRef.id === activity.key) {
        outgoing.push(item)
      }
    })
    return outgoing
  }

  /**
   * 高亮流程图主函数
   * @param bpmnModeler BPMN建模器实例
   * @param activityLists 活动列表
   * @param taskList 任务列表
   * @param processInstance 流程实例
   * @param emit 事件发射器
   */
  const highlightDiagram = async (
    bpmnModeler: any,
    activityLists: Ref<any[]>,
    taskList: Ref<any[]>,
    processInstance: Ref<any>,
    emit: any
  ) => {
    const activityList = activityLists.value
    if (activityList.length === 0) {
      return
    }

    const canvas = bpmnModeler.get('canvas')
    const todoActivity: any = activityList.find((m: any) => !m.endTime) // 找到待办的任务
    const endActivity: any = activityList[activityList.length - 1] // 获得最后一个任务
    let findProcessTask = false // 是否已经高亮了进行中的任务
    const removeTaskDefinitionKeyList: string[] = [] // 进行中高亮之后的任务key集合
    let hasMultiSignNode = false // 是否有多签节点

    const flowElements = getFlowElements(bpmnModeler)
    const nextTasks: any[] = [] // 收集所有待审批任务

    // 遍历流程元素进行高亮处理
    flowElements.forEach((n: any) => {
      const activity: any = activityList.find((m: any) => m.key === n.id)
      if (!activity) return

      // 根据节点类型进行不同的高亮处理
      switch (n.$type) {
        case 'bpmn:UserTask':
          hasMultiSignNode = highlightUserTask(
            n, activity, canvas, taskList.value, findProcessTask, 
            removeTaskDefinitionKeyList, activityList, bpmnModeler
          ) || hasMultiSignNode
          
          const task = taskList.value.find((m: any) => m.id === activity.taskId)
          if (task?.status === 1) {
            findProcessTask = true
          }
          break

        case 'bpmn:ExclusiveGateway':
          highlightExclusiveGateway(n, activity, canvas, activityList)
          break

        case 'bpmn:ParallelGateway':
          highlightParallelGateway(n, activity, canvas, activityList)
          break

        case 'bpmn:StartEvent':
          highlightStartEvent(n, canvas, activityList)
          break

        case 'bpmn:EndEvent':
          highlightEndEvent(n, canvas, processInstance.value)
          break

        case 'bpmn:ServiceTask':
          highlightServiceTask(n, activity, canvas, activityList, bpmnModeler)
          break

        case 'bpmn:SequenceFlow':
          highlightSequenceFlow(n, canvas, activityList)
          break
      }
    })

    // 设置多签图例显示状态
    showMultiSignLegend.value = hasMultiSignNode

    // 过滤任务列表
    if (!isEmpty(removeTaskDefinitionKeyList)) {
      taskList.value = taskList.value.filter(
        item => !removeTaskDefinitionKeyList.includes(item.taskDefinitionKey)
      )
    }

    // 发送下一审批任务
    emit('nextTasks', nextTasks)
  }

  /**
   * 获取流程元素
   */
  const getFlowElements = (bpmnModeler: any) => {
    return bpmnModeler.getDefinitions().rootElements[0].flowElements ||
           bpmnModeler.getDefinitions().rootElements[0].participants?.[0]?.$parent?.participants?.[0]?.processRef.flowElements ||
           []
  }

  /**
   * 高亮用户任务节点
   */
  const highlightUserTask = (
    n: any, activity: any, canvas: any, taskList: any[], 
    findProcessTask: boolean, removeTaskDefinitionKeyList: string[], 
    activityList: any[], bpmnModeler: any
  ): boolean => {
    const task: any = taskList.find((m: any) => m.id === activity.taskId)
    if (!task) return false

    let hasMultiSignNode = false

    // 进行中的任务已经高亮过了，则不高亮后面的任务了
    if (findProcessTask) {
      removeTaskDefinitionKeyList.push(n.id)
      canvas.addMarker(n.id, getResultCss(task.status))
      return hasMultiSignNode
    }

    // 高亮任务
    canvas.addMarker(n.id, getResultCss(task.status))

    // 如果是多签任务，添加多签类型标识
    const multiSignClass = getMultiSignTypeClass(n)
    if (multiSignClass) {
      hasMultiSignNode = true
      canvas.addMarker(n.id, multiSignClass)
    }

    // 如果非通过，就不走后面的线条了
    if (task.status !== 2) {
      return hasMultiSignNode
    }

    // 处理outgoing出线
    const outgoing = getActivityOutgoing(activity, bpmnModeler)
    outgoing?.forEach((nn: any) => {
      const targetActivity: any = activityList.find((m: any) => m.key === nn.targetRef.id)
      if (targetActivity) {
        canvas.addMarker(nn.id, targetActivity.endTime ? 'highlight' : 'highlight-todo')
      } else if (nn.targetRef.$type === 'bpmn:ExclusiveGateway') {
        canvas.addMarker(nn.id, activity.endTime ? 'highlight' : 'highlight-todo')
        canvas.addMarker(nn.targetRef.id, activity.endTime ? 'highlight' : 'highlight-todo')
      } else if (nn.targetRef.$type === 'bpmn:EndEvent') {
        const todoActivity = activityList.find((m: any) => !m.endTime)
        const endActivity = activityList[activityList.length - 1]
        if (!todoActivity && endActivity.key === n.id) {
          canvas.addMarker(nn.id, 'highlight')
          canvas.addMarker(nn.targetRef.id, 'highlight')
        }
        if (!activity.endTime) {
          canvas.addMarker(nn.id, 'highlight-todo')
          canvas.addMarker(nn.targetRef.id, 'highlight-todo')
        }
      }
    })

    return hasMultiSignNode
  }

  /**
   * 高亮排它网关
   */
  const highlightExclusiveGateway = (n: any, activity: any, canvas: any, activityList: any[]) => {
    canvas.addMarker(n.id, getActivityHighlightCss(activity))
    
    let matchNN: any = undefined
    let matchActivity: any = undefined
    n.outgoing?.forEach((nn: any) => {
      const targetActivity = activityList.find((m: any) => m.key === nn.targetRef.id)
      if (!targetActivity) return
      
      if (!matchActivity || matchActivity.type === 'endEvent') {
        matchNN = nn
        matchActivity = targetActivity
      }
    })
    
    if (matchNN && matchActivity) {
      canvas.addMarker(matchNN.id, getActivityHighlightCss(matchActivity))
    }
  }

  /**
   * 高亮并行网关
   */
  const highlightParallelGateway = (n: any, activity: any, canvas: any, activityList: any[]) => {
    canvas.addMarker(n.id, getActivityHighlightCss(activity))
    n.outgoing?.forEach((nn: any) => {
      const targetActivity = activityList.find((m: any) => m.key === nn.targetRef.id)
      if (targetActivity) {
        canvas.addMarker(nn.id, getActivityHighlightCss(targetActivity))
        canvas.addMarker(nn.targetRef.id, getActivityHighlightCss(targetActivity))
      }
    })
  }

  /**
   * 高亮开始事件
   */
  const highlightStartEvent = (n: any, canvas: any, activityList: any[]) => {
    canvas.addMarker(n.id, 'highlight')
    n.outgoing?.forEach((nn: any) => {
      const targetActivity = activityList.find((m: any) => m.key === nn.targetRef.id)
      if (targetActivity) {
        canvas.addMarker(nn.id, 'highlight')
        canvas.addMarker(n.id, 'highlight')
      }
    })
  }

  /**
   * 高亮结束事件
   */
  const highlightEndEvent = (n: any, canvas: any, processInstance: any) => {
    if (!processInstance || processInstance.status === 1) {
      return
    }
    canvas.addMarker(n.id, getResultCss(processInstance.status))
  }

  /**
   * 高亮服务任务
   */
  const highlightServiceTask = (n: any, activity: any, canvas: any, activityList: any[], bpmnModeler: any) => {
    if (activity.startTime > 0 && activity.endTime === 0) {
      // 进入执行，标识进行色
      canvas.addMarker(n.id, getResultCss(1))
    }
    if (activity.endTime > 0) {
      // 执行完成，节点标识完成色，所有outgoing标识完成色
      canvas.addMarker(n.id, getResultCss(2))
      const outgoing = getActivityOutgoing(activity, bpmnModeler)
      outgoing?.forEach(out => {
        canvas.addMarker(out.id, getResultCss(2))
      })
    }
  }

  /**
   * 高亮序列流
   */
  const highlightSequenceFlow = (n: any, canvas: any, activityList: any[]) => {
    const targetActivity = activityList.find((m: any) => m.key === n.targetRef.id)
    if (targetActivity) {
      canvas.addMarker(n.id, getActivityHighlightCss(targetActivity))
    }
  }

  /**
   * 关闭多签图例
   */
  const closeMultiSignLegend = () => {
    showMultiSignLegend.value = false
  }

  return {
    showMultiSignLegend,
    highlightDiagram,
    getActivityHighlightCss,
    getResultCss,
    getMultiSignTypeClass,
    getActivityOutgoing,
    closeMultiSignLegend
  }
}
