import { ref, toRaw, type Ref } from 'vue'
import { getIntDictOptions, DICT_TYPE } from '@/utils/bpmAdapter/bpmDictAdapter'
import { formatDate } from '@/utils/bpmAdapter/formatTime'
import JPGlobal from '@/types/common/jglobal'

/**
 * BPMN覆盖层管理器
 * 负责处理节点hover时的信息显示overlay
 */
export function useOverlayManager() {
  const elementOverlayIds = ref<any>({})
  const overlays = ref<any>(null)
  const currentElement = ref<any>(null)

  /**
   * 初始化覆盖层管理器
   * @param bpmnModeler BPMN建模器实例
   */
  const initOverlays = (bpmnModeler: any) => {
    if (!overlays.value) {
      overlays.value = bpmnModeler.get('overlays')
    }
  }

  /**
   * 清理所有覆盖层
   */
  const clearAllOverlays = () => {
    if (overlays.value) {
      overlays.value.clear()
    }
    elementOverlayIds.value = {}
  }

  /**
   * 移除指定元素的覆盖层
   * @param element 元素
   */
  const removeElementOverlay = (element: any) => {
    if (overlays.value && element) {
      toRaw(overlays.value).remove({ element })
      if (elementOverlayIds.value[element.id]) {
        elementOverlayIds.value[element.id] = null
      }
    }
  }

  /**
   * 生成开始事件的HTML内容
   * @param processInstance 流程实例
   * @returns HTML字符串
   */
  const generateStartEventHtml = (processInstance: any): string => {
    if (!processInstance) return ''

    return `
      <p><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg> 发起人：${
        processInstance?.startUser?.empName || ''
      }${
        processInstance?.startUser?.empCode ? ' (' + processInstance?.startUser?.empCode + ')' : ''
      }</p>
      <p><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 3h18v18H3zM8 12h8"></path><path d="M12 8v8"></path></svg> 部门：${
        processInstance?.startUser?.deptName || ''
      }</p>
      <p><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg> 创建时间：${formatDate(
        processInstance?.createTime
      )}</p>
    `
  }

  /**
   * 生成用户任务的HTML内容
   * @param element 元素
   * @param taskList 任务列表
   * @param bpmnModeler BPMN建模器实例
   * @returns HTML字符串
   */
  const generateUserTaskHtml = (element: any, taskList: any[], bpmnModeler: any): string => {
    // 找到该节点对应的所有任务（多签情况下会有多个）
    const tasks = taskList.filter(m => m.taskDefinitionKey === element.id)
    if (!tasks || tasks.length === 0) {
      return ''
    }

    let html = ''

    // 判断多签类型
    const multiSignInfo = getMultiSignInfo(element, bpmnModeler)
    if (multiSignInfo.isMultiSign) {
      html += `<div class="multi-sign-type ${multiSignInfo.type}">
        <p><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="16" x2="12" y2="12"></line><line x1="12" y1="8" x2="12.01" y2="8"></line></svg>
        ${multiSignInfo.label} | 进度: ${tasks.filter(t => t.status !== 1).length}/${tasks.length}</p>
      </div>`
    }

    // 遍历所有任务，为每个审批人生成展示信息
    tasks.forEach((task, index) => {
      const optionData = getIntDictOptions(DICT_TYPE.BPM_TASK_STATUS)
      let dataResult = ''
      optionData.forEach(element => {
        if (element.value == task.status) {
          dataResult = element.label
        }
      })

      // 如果有多个审批人，添加分隔线
      if (index > 0 && tasks.length > 1) {
        html += `<div style="border-top: 1px dashed #ddd; margin: 4px 0;"></div>`
      }

      html += generateTaskInfo(task, dataResult)
    })

    return html
  }

  /**
   * 生成单个任务信息HTML
   * @param task 任务数据
   * @param dataResult 状态文本
   * @returns HTML字符串
   */
  const generateTaskInfo = (task: any, dataResult: string): string => {
    let html = `
      <p><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg> 审批人：${
        task?.assigneeUser?.empName || ''
      }</p>
      <p><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 3h18v18H3zM8 12h8"></path><path d="M12 8v8"></path></svg> 部门：${
        task?.assigneeUser?.deptName || ''
      }</p>
      <p><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg> 结果：${dataResult}</p>
      <p><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg> 创建时间：${formatDate(
        task.createTime
      )}</p>
    `

    if (task.endTime) {
      html += `<p><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="8 12 12 16 16 12"></polyline><line x1="12" y1="8" x2="12" y2="16"></line></svg> 结束时间：${formatDate(
        task.endTime
      )}</p>`
    }

    if (task.reason) {
      html += `<p><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg> 审批建议：${task.reason}</p>`
    }

    // 签名信息
    if (task.needSign && task.status != 1) {
      html += `
        <div style="margin-top: 10px; font-weight: normal">
          <span style="font-weight: normal">
            <svg xmlns="http://www.w3.org/2000/svg" class="preview-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M15.5 3H5a2 2 0 0 0-2 2v14c0 1.1.9 2 2 2h14a2 2 0 0 0 2-2V8.5L15.5 3Z"></path><path d="M15 3v6h6"></path></svg>
            签名：
          </span>
          <img width="60" src="${JPGlobal.getRealOCUrl(task.signUrl)}" />
        </div>
      `
    }

    // 附件信息
    if (task.needAttachment && task.status != 1 && task.attachmentList && task.attachmentList.length > 0) {
      html += `
        <div style="margin-top: 10px; font-weight: normal">
          <span>
            <svg xmlns="http://www.w3.org/2000/svg" class="preview-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline></svg>
            附件：
          </span>
          <ul>
            ${task.attachmentList.map((attachment: any) => `<li>${attachment.label}</li>`).join('')}
          </ul>
        </div>
      `
    }

    return html
  }

  /**
   * 生成服务任务的HTML内容
   * @param activity 活动数据
   * @returns HTML字符串
   */
  const generateServiceTaskHtml = (activity: any): string => {
    let html = ''
    
    if (activity.startTime > 0) {
      html = `<p><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg> 创建时间：${formatDate(
        activity.startTime
      )}</p>`
    }
    
    if (activity.endTime > 0) {
      html += `<p><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="8 12 12 16 16 12"></polyline><line x1="12" y1="8" x2="12" y2="16"></line></svg> 结束时间：${formatDate(
        activity.endTime
      )}</p>`
    }
    
    return html
  }

  /**
   * 生成结束事件的HTML内容
   * @param processInstance 流程实例
   * @returns HTML字符串
   */
  const generateEndEventHtml = (processInstance: any): string => {
    if (!processInstance) return ''

    const optionData = getIntDictOptions(DICT_TYPE.BPM_TASK_STATUS)
    let dataResult = ''
    optionData.forEach(element => {
      if (element.value == processInstance.status) {
        dataResult = element.label
      }
    })

    let html = `<p><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg> 结果：${dataResult}</p>`
    
    if (processInstance.endTime) {
      html += `<p><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="8 12 12 16 16 12"></polyline><line x1="12" y1="8" x2="12" y2="16"></line></svg> 结束时间：${formatDate(
        processInstance.endTime
      )}</p>`
    }
    
    return html
  }

  /**
   * 获取多签信息
   * @param element 元素
   * @param bpmnModeler BPMN建模器实例
   * @returns 多签信息对象
   */
  const getMultiSignInfo = (element: any, bpmnModeler: any) => {
    const flowElements = bpmnModeler.getDefinitions().rootElements[0].flowElements
    const nodeDefinition = flowElements.find((item: any) => item.id === element.id)

    if (!nodeDefinition || !nodeDefinition.loopCharacteristics) {
      return { isMultiSign: false, type: '', label: '' }
    }

    const isMultiInstance = nodeDefinition.loopCharacteristics.$type === 'bpmn:MultiInstanceLoopCharacteristics'
    if (!isMultiInstance) {
      return { isMultiSign: false, type: '', label: '' }
    }

    const isSequential = nodeDefinition.loopCharacteristics.isSequential || false
    const completionCondition = nodeDefinition.loopCharacteristics.completionCondition?.body || ''
    const isOrSign = completionCondition.includes('nrOfCompletedInstances > 0')
    const isAndSign = completionCondition.includes('>= nrOfInstances')

    if (isSequential) {
      return { isMultiSign: true, type: 'sequential', label: '串行多签' }
    } else if (isOrSign) {
      return { isMultiSign: true, type: 'or', label: '或签（任一人审批即可）' }
    } else if (isAndSign) {
      return { isMultiSign: true, type: 'and', label: '会签（需所有人审批）' }
    } else {
      return { isMultiSign: true, type: 'parallel', label: '并行多签' }
    }
  }

  /**
   * 显示元素信息覆盖层
   * @param element 元素
   * @param activityLists 活动列表
   * @param taskList 任务列表
   * @param processInstance 流程实例
   * @param bpmnModeler BPMN建模器实例
   */
  const showElementOverlay = (
    element: any,
    activityLists: Ref<any[]>,
    taskList: Ref<any[]>,
    processInstance: Ref<any>,
    bpmnModeler: any
  ) => {
    if (!element || element.type === 'bpmn:Process') return

    currentElement.value = element
    
    if (!elementOverlayIds.value) {
      elementOverlayIds.value = {}
    }

    initOverlays(bpmnModeler)

    const activity = activityLists.value.find(m => m.key === element.id)
    if (!activity) return

    let html = ''

    // 根据元素类型生成不同的HTML内容
    switch (element.type) {
      case 'bpmn:StartEvent':
        html = generateStartEventHtml(processInstance.value)
        break
      case 'bpmn:UserTask':
        html = generateUserTaskHtml(element, taskList.value, bpmnModeler)
        break
      case 'bpmn:ServiceTask':
        html = generateServiceTaskHtml(activity)
        break
      case 'bpmn:EndEvent':
        html = generateEndEventHtml(processInstance.value)
        break
    }

    if (html) {
      // 添加节点ID信息
      html += `<p class="node-id"><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="16" x2="12" y2="12"></line><line x1="12" y1="8" x2="12.01" y2="8"></line></svg> 节点ID: ${element.id}</p>`

      // 创建覆盖层
      elementOverlayIds.value[element.id] = toRaw(overlays.value)?.add(element, {
        position: { left: 0, bottom: 0 },
        html: `<div class="element-overlays">${html}</div>`,
      })
    }
  }

  return {
    elementOverlayIds,
    overlays,
    currentElement,
    initOverlays,
    clearAllOverlays,
    removeElementOverlay,
    showElementOverlay
  }
}
