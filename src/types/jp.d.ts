import { DefineComponent } from 'vue'

// 导入所有组件类型
import * as comps from '@/types/common/jcomponents'

import AdaptiveDataTable from '@/components/common/crud/components/AdaptiveDataTable.vue'
const test = comps.Select
// 声明全局组件

declare module 'vue' {
  export interface GlobalComponents {
    'j-container': typeof comps.Container
    'j-title-line': typeof comps.TitleLine
    'j-crud': typeof comps.CRUD
    'j-n-data-table': typeof AdaptiveDataTable
    'j-select': typeof comps.Select
    'j-context-menu': typeof comps.ContextMenu
    'j-hospital': typeof comps.Hospital
    'j-upload': typeof comps.Upload
    'j-export': typeof comps.Export
    'j-flow': typeof comps.Flow
    'j-org-flow': typeof comps.OrgFlow
    'j-org': typeof comps.Org
    'j-organization-flow': typeof comps.OrganizationFlow
    'j-text-edit': typeof comps.TextEdit
    'j-icon': typeof comps.Icon
    'j-bpmn': typeof comps.Bpmn
    'j-preview': typeof comps.NPreview
    'j-npreview': typeof comps.NPreview
    'j-image': typeof comps.Image
    'j-column': typeof comps.Column
    'j-action-form': typeof comps.ActionForm
    'j-bus-emp-search': typeof comps.EmpSearch
    'j-bus-hos-org': typeof comps.HosOrg
    'j-bus-asset-search': typeof comps.AssetSearch
    'j-modal': typeof comps.Modal
    'j-audit-flow': typeof comps.AuditFlow
    'j-sign': typeof comps.Sign
    'j-search-title': typeof comps.SearchTitle
    'j-form-item': typeof comps.FormItem
    'j-bus-audit-select': typeof comps.AuditSelect
    'j-bus-audit-progress': typeof comps.AuditProgress
    'j-bus-audit-execution': typeof comps.AuditExecution
    'j-bus-audit-steps': typeof comps.AuditSteps
    'j-bus-addr': typeof comps.Addr
    'j-bus-invoice-identify': typeof comps.InvoiceIdentify
    'j-upload-preview': typeof comps.UploadPreview
    'j-financial-account': typeof comps.FinancialAccount
    ContentWrap: typeof comps.ContentWrap
    'dict-tag': typeof comps.DictTag
  }
}

// 声明全局属性
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $jglobal: InstanceType<typeof import('@/types/common/jglobal').default>
  }
}
