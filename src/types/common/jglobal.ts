import { Component, h, reactive, toRaw } from 'vue'
import { MenuOption, NIcon, NPopover, NTag } from 'naive-ui'
import { CompSize, useNaiveConfig } from '@/types/comps/naive'
import { NaiveComponentSize } from '@/types/enums/enums'
import { StoreSysDict } from '@/types/modules/sys'
import { useSysStore, useUserStore } from '@/store'
import { JFeedback, Option } from '@/types/comps/common'
import { deleteUserToken } from '@/api/user'
import { Step } from '@/types/comps/auditForm'
import { getSysConfig } from '@/api/sys/config'
import { Audit, AuditDetail, IRes } from '@jtypes'
// @ts-ignore
import html2canvas from 'html2canvas'
import { RouteRecordRaw, RouterLink } from 'vue-router'
import { BookOutline as BookIcon, FolderOpenOutline as FolderOpenOutlineIcon } from '@vicons/ionicons5'

import BMF from 'browser-md5-file'
import dayjs from 'dayjs'

// import * as moment from 'moment'

// 全局naive设置
const naiveConfig = useNaiveConfig()

// 全局对象，单例响应式
class JPGlobal {
  naiveCompsSize: CompSize = naiveConfig.size
  static gatewayPath = ['/gateway', '/updatePwd', '/personCenter']
  static linkStyle = {
    textDecoration: 'underline #18a058',
    color: '#18a058',
    cursor: 'pointer',
  }
  private static instance: JPGlobal = reactive(new JPGlobal())

  private constructor() {}

  static getInstance(): JPGlobal {
    return this.instance
  }

  // 设置组件大小
  static setCompsSize(size: NaiveComponentSize) {
    this.instance.naiveCompsSize = size
  }

  // 获取组件大小
  static getCompsSize(): CompSize {
    return this.instance.naiveCompsSize
  }

  /**
   * 渲染 ionicons5
   * @param icon 图标
   * @returns
   */
  static renderIcon(icon: Component) {
    return () => h(NIcon, null, { default: () => h(icon) })
  }

  /**
   * 日期格式化
   * @param datetime 日期时间
   * @param pattern 格式
   * <AUTHOR>
   */
  static formatDatetime(datetime: Date | number | string, pattern: string = 'yyyy-MM-dd HH:mm:ss'): string {
    // 如果传入的是数字(时间戳)、日期字符串
    if (typeof datetime === 'number' || typeof datetime === 'string') {
      datetime = new Date(datetime)
    }

    // 一些常用的格式化函数
    const formatFunctions: { [key: string]: () => string } = {
      yyyy: () => datetime.getFullYear().toString(),
      yy: () => datetime.getFullYear().toString().slice(-2),
      MM: () => (datetime.getMonth() + 1).toString().padStart(2, '0'),
      M: () => (datetime.getMonth() + 1).toString(),
      dd: () => datetime.getDate().toString().padStart(2, '0'),
      d: () => datetime.getDate().toString(),
      HH: () => datetime.getHours().toString().padStart(2, '0'),
      H: () => datetime.getHours().toString(),
      mm: () => datetime.getMinutes().toString().padStart(2, '0'),
      m: () => datetime.getMinutes().toString(),
      ss: () => datetime.getSeconds().toString().padStart(2, '0'),
      s: () => datetime.getSeconds().toString(),
      SSS: () => datetime.getMilliseconds().toString().padStart(3, '0'),
      SS: () => datetime.getMilliseconds().toString().padStart(3, '0').slice(0, 2),
      S: () => datetime.getMilliseconds().toString().padStart(3, '0').slice(0, 1),
    }

    // 使用正则表达式替换pattern中的格式占位符
    return pattern.replace(/yyyy|yy|MM|M|dd|d|HH|H|mm|m|ss|s|SSS|SS|S/g, match => {
      return formatFunctions[match]()
    })
  }

  /**
   * 日期格式化
   * @param timestamp 日期
   * @param fmt 格式化规则
   * @returns
   */
  static timestampToTime = (timestamp: number, fmt: string = 'yyyy-MM-dd') => {
    let date = new Date(timestamp)
    if (/(y+)/.test(fmt)) {
      fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
    }
    let o = {
      'M+': date.getMonth() + 1,
      'd+': date.getDate(),
      'h+': date.getHours(),
      'm+': date.getMinutes(),
      's+': date.getSeconds(),
    } as any

    // 遍历这个对象
    for (let k in o) {
      if (new RegExp(`(${k})`).test(fmt)) {
        let str = o[k] + ''
        fmt = fmt.replace(RegExp.$1, RegExp.$1.length === 1 ? str : padLeftZero(str))
      }
    }

    function padLeftZero(str: any) {
      return ('00' + str).substr(str.length)
    }

    return fmt
  }

  /**
   * 深拷贝
   * @param obj 对象
   * @returns
   */
  // static deepCopy = (obj: any): any => {
  //   return JSON.parse(JSON.stringify(obj))
  // }

  static deepCopy = (obj: any) => {
    try {
      // 处理 Vue ref 对象,如果是ref则获取其value值
      if (obj && obj.hasOwnProperty('__v_isRef')) {
        obj = obj.value
      }

      let objClone: any = Array.isArray(obj) ? [] : {}

      if (obj && typeof obj === 'object') {
        for (let key in obj) {
          if (obj.hasOwnProperty(key)) {
            let value = obj[key]

            // 处理属性值为ref的情况
            if (value && value.hasOwnProperty('__v_isRef')) {
              value = value.value
            }

            // 判断是否为对象,如果是则递归复制
            if (value && typeof value === 'object') {
              objClone[key] = this.deepCopy(value)
            } else {
              // 如果不是对象,直接赋值
              objClone[key] = value
            }
          }
        }
      }
      return objClone
    } catch (error) {
      console.error('深拷贝出错:', error)
      return obj
    }
  }

  // 对象数组去重
  static repeat = (list3: any, key: any) => {
    let map = new Map()
    return list3.filter((item: any) => !map.has(item[key].toString()) && map.set(item[key].toString(), ''))
  }

  /**
   * 通过字典类型获取字典
   * @param type 字典类型
   * @returns
   */
  static getDictByType = (type: string | undefined): Array<StoreSysDict> => {
    const sysStore = useSysStore()
    let dicts: Array<StoreSysDict> = sysStore.getDictByType(type)
    return dicts
  }

  /**
   * 通过字典值获取字典标签
   * @param type 字典类型
   * @param value 值
   * @returns label
   */
  static getDictLabelByValue = (type: string | undefined, value: string): string => {
    const sysStore = useSysStore()
    let dicts: Array<StoreSysDict> = sysStore.getDictByType(type)
    let label = ''
    if (dicts != undefined && dicts.length > 0) {
      dicts.forEach((dict: StoreSysDict) => {
        if (dict.value === value) {
          label = dict.label
          return
        }
      })
    }
    return label
  }

  /**
   * 通过下标获取标签名称
   * @param idx 下标
   */
  static getTagNameByIndex = (idx: number = 0): CompSize => {
    const tagNames = ['success', 'warning', 'info', 'error', 'primary']
    if (idx > tagNames.length) {
      return tagNames[0] as CompSize
    }
    return tagNames[idx] as CompSize
  }

  /**
   * 页面按钮权限
   * @param name 按钮名称
   * @returns true：有权限 false：无权限
   */
  static pageButtonAuth = (name: string, path: string) => {
    const userStore = useUserStore()
    let pageAuthObj = userStore.getUserPageAuth[path.substring(1)]
    if (pageAuthObj !== undefined && pageAuthObj[name] != undefined) {
      if (pageAuthObj[name] > 0) {
        return true
      } else {
        return false
      }
    }
    return true
  }

  /**
   * 是门户页面
   * @param path 当前路径
   * @returns
   */
  static isGateway = (path: string): boolean => {
    return this.gatewayPath.includes(path)
  }

  /**
   * 防抖
   * @param fn 函数
   * @param delay 延迟时间 单位：ms
   * <AUTHOR>
   */
  static debounce = (fn: (...args: any[]) => any, delay = 500) => {
    let timer: NodeJS.Timeout | null = null
    return (...args: any[]) => {
      if (timer) clearTimeout(timer)
      timer = setTimeout(() => {
        fn.apply(this, args)
      }, delay)
    }
  }

  /**
   * 脱敏函数
   * @param str 需要脱敏的字符串
   * @param start 开头保留的字符数
   * @param end 结尾保留的字符数
   * @param maskChar 用于脱敏的字符，默认为 '*'
   * @returns 脱敏后的字符串
   * <AUTHOR>
   */
  static desensitize(str: string, start: number, end: number, maskChar: string = '*'): string {
    if (!str || str.length <= start + end) {
      // 如果字符串不存在、长度小于或等于需要保留的字符数总和，则返回原始字符串
      return str
    }

    const startPart = str.substring(0, start)
    const endPart = str.substring(str.length - end)
    const maskLength = str.length - start - end
    const mask = maskChar.repeat(maskLength)

    return startPart + mask + endPart
  }

  /**
   * 数组转树状结构
   * @example
   * array2Tree(data, 0, idKey: 'id', parentIdKey: 'parentId', nameKey: 'name', sort: (a, b) => a.sort - b.sort)
   * @param data 要转换的数据
   * @param rootId 根节点值
   * @param config 配置项
   * @param config.idKey id字段名
   * @param config.parentIdKey 父级id字段名
   * @param config.nameKey 名称字段名
   * @param config.sort 排序字段名(该字段需要为 <code>number</code> 类型，将按照该字段升序排列) | 排序函数
   * @return {Common.TreeNode[]} 转换后的树形数据
   * <AUTHOR>
   */
  static array2Tree<T, K = number>(
    data: T[],
    rootId: K = null,
    config: {
      idKey: keyof T & string
      parentIdKey: keyof T & string
      nameKey?: keyof T & string
      sort?: (keyof T & string) | ((a: T, b: T) => number)
    }
  ): Common.TreeNode<T>[] {
    // 判断是否是数组 不是数组就返回 []
    if (!Array.isArray(data) || !data.length) return []
    const { idKey, parentIdKey, nameKey, sort } = config
    let newArr: Common.TreeNode<T>[] = []
    let children: Common.TreeNode<T>[] = []

    data = this.deepCopy(data)

    data.forEach(item => {
      if (item[parentIdKey] == rootId) {
        children = this.array2Tree(data, item[idKey], config)
        newArr.push({
          ...item,
          children: children.length > 0 ? children : undefined,
          key: item[idKey],
          value: item[idKey],
          label: nameKey ? item[nameKey] : undefined,
          lowNum: children.length,
        })
      }
    })
    if (sort && typeof sort === 'function') {
      newArr.sort((a, b) => sort(a, b))
    } else if (typeof sort === 'string') {
      newArr.sort((a, b) => {
        return a[sort] - b[sort]
      })
    } else {
      newArr.sort((a, b) => {
        return a[idKey] - b[idKey]
      })
    }
    return newArr
  }

  /**
   * 获取树状数据
   * @param data 数据
   * @param id 子级节点名称
   * @param parentId 父级节点名称
   * @param label 标签字段
   */
  static getTreeNode = (data: Array<any>, id: string | number, parentId: string, label: string = '') => {
    // 当数据为空时，直接返回空数组
    if (!data || data.length === 0) {
      return []
    }
    // 对数据进行深拷贝，防止对源数据进行修改
    data = this.deepCopy(data)
    let treeData: any = []
    data.forEach(item => {
      if (!item[parentId]) {
        treeData.push(item)
      }
      getChildren(data, treeData, id, parentId, label)
    })
    treeData.sort((a: any, b: any) => {
      return a[id] - b[id]
    })
    return treeData
  }
  /**
   * 生成下拉菜单Option
   * @param data
   * @param label
   * @param value
   */
  static getSelectOption = (data: Array<any>, label: string, value: string): Array<Option> => {
    let list = [] as Array<Option>
    data.forEach((item: any) => {
      list.push({ label: item[label] + '', value: item[value] + '', ori: item })
    })
    return list
  }

  /**
   * 自定义表单校验
   * @param flag 是否报错
   * @param value 当前校验值
   * @param item curd列
   * @param msg 提示信息
   * @returns
   */
  static feedbackValidate = (flag: boolean, value: any, item: JFeedback, msg: string, check: Function) => {
    if (flag && check()) {
      item.validationStatus = 'error'
      item.feedbackText = msg
      return
    }
    if (value) {
      item.validationStatus = undefined
    }
    item.feedbackText = ''
  }

  /**
   * 获取颜色
   * @returns 颜色
   */
  static getColors = () => {
    return [
      '#25a1ff',
      '#51d8b9',
      '#91cc75',
      '#faca60',
      '#7ac2df',
      '#65ceca',
      '#b2db9e',
      '#fe8e85',
      '#68adf6',
      '#19CAAD',
      '#8CC7B5',
      '#A0EEE1',
      '#90b2bf',
      '#a3d0ad',
      '#D6D5B7',
      '#D1BA74',
      '#E6CEAC',
      '#ECAD9E',
      '#F4606C',
      '#A3C1EA',
      '#F8C8C8',
      '#71D8D2',
      '#F7EE94',
      '#CBB4E3',
      '#fa5d5d',
    ]
  }

  /**
   * 文件下载，res必须为blob类型
   * @param res 返回数据
   * @param fileName 文件名称
   */
  static download(res: any, fileName: string) {
    const blob = new Blob([res])
    const a = document.createElement('a')
    if (!a.click) {
      throw new Error('DownloadManager: "a.click()" is not supported.')
    }
    a.href = URL.createObjectURL(blob)
    a.target = '_parent'
    a.download = fileName
    ;(document.body || document.documentElement).appendChild(a)
    a.click()
    a.remove()
  }

  /**
   * 获取下载文件名称
   * @param filePath 存储文件路径
   * @param fileName 文件名称
   */
  static getDownloadFileName(filePath: string, fileName: string) {
    if (!filePath) return filePath
    return fileName + filePath.substring(filePath.lastIndexOf('.'))
  }

  /**
   * 获取下拉选数据
   * @param data 数据
   * @param prop 需要转换的label和value的key
   */
  static genSelectionData(data: Array<any>, prop: Option) {
    // let selection: Array<Option> = []
    // for (let d of data) {
    //   selection.push({
    //     label: d[prop.label],
    //     value: d[prop.value],
    //   })
    // }
    // return selection
    return genChildren(data, prop)
  }

  /**
   * 通过id退出
   * @param ids 用户id
   * @param all 是否全部用户退出，当前为true时，id可不填
   */
  static logoutByUserIds(ids: Array<string | number> = [], all: boolean = false) {
    deleteUserToken({ exitAllUser: all, ids }).then(() => {})
  }

  static clearObject(obj: any) {
    let source = JPGlobal.deepCopy(obj)
    clearAll(source)
    return source
  }

  /**
   * 取出两个数组内的不同元素
   * @param arr1
   * @param arr2
   */
  static getArrDifference(arr1: Array<any>, arr2: Array<any>) {
    return arr1.concat(arr2).filter((v, i, arr) => {
      return arr.indexOf(v) === arr.lastIndexOf(v)
    })
  }

  /**
   * 获取日期范围快捷选项
   * @param options 配置选项,可选返回哪些快捷选项
   */
  static getRangeShortcuts(options?: {
    currentMonth?: boolean
    lastMonth?: boolean
    currentQuarter?: boolean
    lastQuarter?: boolean
    currentYear?: boolean
    lastYear?: boolean
  }): Record<string, () => [number, number]> {
    const shortcuts: Record<string, () => [number, number]> = {}

    if (options?.currentMonth ?? true) {
      shortcuts['当月'] = () => {
        const end = dayjs()
        const start = dayjs().startOf('month')
        return [start.valueOf(), end.valueOf()]
      }
    }

    if (options?.lastMonth ?? true) {
      shortcuts['上月'] = () => {
        const start = dayjs().subtract(1, 'month').startOf('month')
        const end = dayjs().subtract(1, 'month').endOf('month')
        return [start.valueOf(), end.valueOf()]
      }
    }

    if (options?.currentYear ?? true) {
      shortcuts['当年'] = () => {
        const start = dayjs().startOf('year')
        const end = dayjs()
        return [start.valueOf(), end.valueOf()]
      }
    }

    if (options?.lastYear ?? true) {
      shortcuts['去年'] = () => {
        const start = dayjs().subtract(1, 'year').startOf('year')
        const end = dayjs().subtract(1, 'year').endOf('year')
        return [start.valueOf(), end.valueOf()]
      }
    }

    return shortcuts
  }

  /**
   * 获取审核步骤
   * @param item
   */
  static getSteps(item: Audit) {
    let steps: Step[] = []
    const userStore = useUserStore()
    item.details.sort((a, b) => a.chkSeq - b.chkSeq)

    item.details.forEach(detail => {
      detail.sign = JPGlobal.getRealOCUrl(detail.sign as string)
      // 限制当前科室
      let rtdDeptCode
      if (detail.rtdDept && detail.rtdDept == '1') {
        rtdDeptCode = userStore.getUserInfo.hrmUser?.hrmOrgId as string
      }
      steps.push({
        title: detail.chkDeptName,
        titleTag: h(
          NPopover,
          { trigger: 'hover' },
          {
            trigger: () =>
              h(NTag, { type: 'success', size: 'small' }, () =>
                detail.actChkerName ? detail.actChkerName : detail.chkerName ?? '待指定'
              ),
            default: () =>
              detail.chkerName && detail.chkerName.includes(',') ? '可审核人: ' + detail.chkerName : '审核人',
          }
        ),
        data: {
          dept: detail.chkDept,
          deptOption: {},
          chker: detail.chker,
          chkerOption: {},
          attachment: detail.chkAtt,
          sign: detail.chkSign,
          desc: detail.chkRemarks,
          chkerName: detail.chkerName,
          dscr: detail.dscr,
          rtdDept: detail.rtdDept,
          jobCategory: detail.jobCategory ? detail.jobCategory.split(',') : [],
          rtdDeptCode,
          detail,
        },
        key: detail.chkSeq - 1,
      })
    })
    return steps
  }

  /**
   * 通过steps生成审核数据
   * @param allSteps
   */
  static getAuditDetails(allSteps: Step[]) {
    let details: any[] = []
    allSteps.forEach((step, idx) => {
      let d = step.data
      if (d !== undefined) {
        details.push({
          // chker: d.chker?.join(','), // 多选
          chker: d.chker,
          chkDept: d.dept,
          chkDeptName: d.deptOption?.label,
          chkDeptOption: d.deptOption,
          chkRemarks: d.desc,
          chkSign: d.sign,
          chkAtt: d.attachment,
          chkSeq: idx + 1,
          dscr: d.dscr,
          rtdDept: d.rtdDept,
          jobCategory: d.jobCategory?.join(','),
        })
      }
    })
    return details
  }

  /**
   * base64字符串转file类型
   * @param base64String
   * @param fileName 文件名称
   * @param type 类型
   */
  static base64ToFile(base64String: string, fileName: string = 'temp.png', type: string = 'image/png') {
    if (base64String) {
      // 去掉Base64字符串的前缀（如果有）
      const dataString = base64String.split(',')[1]

      // 将Base64字符串转换为二进制数据（ArrayBuffer）
      const arrayBuffer = Uint8Array.from(atob(dataString), c => c.charCodeAt(0))

      // 使用ArrayBuffer创建Blob对象
      const blob = new Blob([arrayBuffer], { type: 'image/png' })

      // 使用Blob对象创建File对象
      return new File([blob], fileName, { type: blob.type })
    }
    return null
  }

  /**
   * 格式化金钱
   * example：
   *  input: 12345678 mode = 'w' isFormat = true
   *  output: 123.46 /万
   *
   *  input: 12345678 mode = 'w' isFormat = false
   *  output: 12345678 /元
   * @param data 金钱
   * @param isFormat 是否格式化
   * @param fractionDigits
   */
  static formatCost(data: any, isFormat: boolean, fractionDigits = 3) {
    if (!data) {
      return 0
    }
    let resVal = data
    let size = 'font-size: 10px'
    if (data) {
      if (isFormat) {
        if (Math.abs(parseFloat(data) / 100000000) > 1) {
          resVal = (parseFloat(data) / 100000000).toFixed(fractionDigits) + '<span style="' + size + '">/亿</span>'
        } else if (Math.abs(parseFloat(data) / 10000) > 1) {
          resVal = (parseFloat(data) / 10000).toFixed(fractionDigits) + '<span style="' + size + '">/万</span>'
        }
      }
    }
    return resVal
  }

  /**
   * 格式化金额
   * @example 12345678.99 => ¥12,345,678.99
   * @param amount 待格式化的金额
   * @param withCurrency 是否包含货币符号
   * @param decimalPlaces 小数位数
   */
  static formatMoney(amount: number, withCurrency: boolean = true, decimalPlaces: number = 2): string {
    return amount.toLocaleString('zh', {
      style: withCurrency ? 'currency' : undefined,
      currency: withCurrency ? 'CNY' : undefined,
      minimumFractionDigits: decimalPlaces,
      maximumFractionDigits: decimalPlaces,
    })
  }

  /**
   * 获取转换后外链地址
   * @param url 转换前外链地址
   */
  static getRealOCUrl(url: string) {
    if (!url) {
      return ''
    }
    let baseUrl = process.env.BASE_URL

    let PROD = import.meta.env.PROD
    // PROD = true
    // if (true) {
    if (true || (PROD && url && baseUrl)) {
      let pattern = /^https?\:\/\/([^\/]+)\:(\d+).*/
      let result = baseUrl.match(pattern)
      if (result) {
        let ip = result[1]
        let port = parseInt(result[2])
        let ipPattern = /^(https?:\/\/)(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}):(\d+)/
        // let replacement = process.env.BASE_URL + '/oss/'
        let prefix = 'https://'
        let replacement = ip + ':' + port + '/oss/'
        replacement = url.replace(ipPattern, replacement)
        // 删除重复的https:/ http://
        // return prefix + replacement.replace(/^(https?:\/\/)/, '').replace('//', '/')
        url = prefix + replacement.replace('//', '/')
      }
    }
    if (!url) {
      return ''
    }

    if (url.startsWith('https://https:/')) {
      url = url.replace('https://https:/', 'https://')
    }
    return url
  }

  /**
   * 将骆驼命名规则的字符串转换成使用短横线命名法的字符串
   * @param str
   */
  static getKebabCase2(str: string) {
    return str.replace(/[A-Z]/g, function (item) {
      return '_' + item.toLowerCase()
    })
  }

  /**
   * 数字转大写
   * @param n
   */
  static numberToChineseCapital(money: number) {
    let cnNums = new Array('零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖') //汉字的数字
    let cnIntRadice = new Array('', '拾', '佰', '仟') //基本单位
    let cnIntUnits = new Array('', '万', '亿', '兆') //对应整数部分扩展单位
    let cnDecUnits = new Array('角', '分', '毫', '厘') //对应小数部分单位
    let cnIntLast = '元' //整型完以后的单位
    let maxNum = 999999999999999.99 //最大处理的数字
    let IntegerNum: string = '' //金额整数部分
    let DecimalNum: string = '' //金额小数部分
    let ChineseStr = '' //输出的中文金额字符串
    let parts: string[] = ['', ''] //分离金额后用的数组，预定义
    let zeroCount, IntLen, n, p, q, m, decLen
    if (money == undefined) {
      return ''
    }
    if (money >= maxNum) {
      return '超出最大处理数字'
    }
    if (money == 0) {
      ChineseStr = cnNums[0] + cnIntLast
      return ChineseStr
    }
    let head = money < 0 ? '欠' : ''
    money = Math.abs(money)
    if (money.toString().indexOf('.') == -1) {
      IntegerNum = money.toString()
      DecimalNum = ''
      cnIntLast = '元整'
    } else {
      parts = money.toString().split('.')
      IntegerNum = parts[0]
      DecimalNum = parts[1].substr(0, 2)
    }
    if (parseInt(IntegerNum, 10) > 0) {
      //获取整型部分转换
      zeroCount = 0
      IntLen = IntegerNum.length
      for (let i = 0; i < IntLen; i++) {
        n = IntegerNum.substr(i, 1)
        p = IntLen - i - 1
        q = p / 4
        m = p % 4
        if (n == '0') {
          zeroCount++
        } else {
          if (zeroCount > 0) {
            ChineseStr += cnNums[0]
          }
          zeroCount = 0 //归零
          ChineseStr += cnNums[parseInt(n)] + cnIntRadice[m]
        }
        if (m == 0 && zeroCount < 4) {
          ChineseStr += cnIntUnits[q]
        }
      }
      ChineseStr += cnIntLast
      //整型部分处理完毕
    }
    if (DecimalNum != '') {
      //小数部分
      decLen = DecimalNum.length
      for (let i = 0; i < decLen; i++) {
        n = DecimalNum.substr(i, 1)
        if (n != '0') {
          ChineseStr += cnNums[Number(n)] + cnDecUnits[i]
        }
      }
    }
    if (ChineseStr == '') {
      ChineseStr += cnNums[0] + cnIntLast
    }
    return head + ChineseStr
  }

  // static numberToChineseCapital(n: number) {
  //   const fraction = ['角', '分']
  //   const digit = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
  //   const unit = [
  //     ['元', '万', '亿'],
  //     ['', '拾', '佰', '仟'],
  //   ]
  //   let head = n < 0 ? '欠' : ''
  //   n = Math.abs(n)
  //
  //   let s = ''
  //   for (let i = 0; i < fraction.length; i++) {
  //     s += (digit[Math.floor(n * 10 * Math.pow(10, i)) % 10] + fraction[i]).replace(/零./, '')
  //   }
  //   s = s || '整'
  //   n = Math.floor(n)
  //
  //   for (let i = 0; i < unit[0].length && n > 0; i++) {
  //     let p = ''
  //     for (let j = 0; j < unit[1].length && n > 0; j++) {
  //       p = digit[n % 10] + unit[1][j] + p
  //       n = Math.floor(n / 10)
  //     }
  //     s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s
  //   }
  //   return (
  //     head +
  //     s
  //       .replace(/(零.)*零元/, '元')
  //       .replace(/(零.)+/g, '零')
  //       .replace(/^整$/, '零元整')
  //   )
  // }

  /**
   * 获取系统参数
   */
  static async getSysConfig() {
    let config = {}
    await getSysConfig({}).then((res: IRes) => {
      config = res.data
    })
    return config
  }

  /**
   * 检查审核详情
   * @param details
   */
  static checkAuditDetails(details: AuditDetail[]) {
    if (details && details.length > 0 && Object.keys(details).length > 0) {
      for (let detail of details) {
        if (!detail.chker || !detail.chkDept) {
          return false
        }
      }
      return true
    }
    return false
  }

  /**
   * 添加数组内容到formdata
   * @param formData
   * @param arr
   * @param fieldName
   * @param fileField
   */
  static addArrToFormData(formData: FormData, arr: any[], fieldName: string, fileField: string = '') {
    for (let i = 0; i < arr.length; i++) {
      let td = arr[i]
      for (let tdKey in td) {
        let pk = fieldName + '[' + i + '].' + tdKey
        if (fileField && tdKey == fileField && Array.isArray(td[tdKey])) {
          let tfs: File[] = td[tdKey]
          for (let j = 0; j < tfs.length; j++) {
            formData.append(pk + 'Files[' + j + ']', tfs[j])
          }
        } else {
          formData.append(pk, td[tdKey] ? td[tdKey] : '')
        }
      }
    }
  }

  /**
   * 生成UUID
   */
  static guid() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      let r = (Math.random() * 16) | 0,
        v = c == 'x' ? r : (r & 0x3) | 0x8
      return v.toString(16)
    })
  }

  /**
   * 页面转图片
   */
  static async pageToPng(el: any) {
    let file
    // @ts-ignore
    await html2canvas(el, {
      height: el.scrollHeight,
      width: el.scrollWidth,
    }).then((canvas: any) => {
      let dataURL = canvas.toDataURL('image/png')
      let arr = dataURL.split(','),
        // @ts-ignore
        type = arr[0].match(/:(.*?);/)[1], //获取MIME 类型，即image/png
        bstr = atob(arr[1]),
        count = bstr.length,
        u8arr = new Uint8Array(count)
      while (count--) {
        u8arr[count] = bstr.charCodeAt(count)
      }
      let blob = new Blob([u8arr], {
        type: type,
      })
      // let url = URL.createObjectURL(blob)
      // window.open(url) //创建一个新的浏览器窗口对象, 参数指定了该窗口将会打开的地址
      file = new File([blob], 'page.png', { type: blob.type })
    })
    return file
  }

  static reduceData(mo: any, type: string) {
    const groupedData = mo.reduce((accumulator: any, current: any) => {
      const key = current[type]
      if (!accumulator[key]) {
        accumulator[key] = []
      }
      accumulator[key].push(current)
      return accumulator
    }, {})
    let res1: any = []
    Object.keys(groupedData).forEach((key: any, index: any) => {
      res1.push({ key: index, [type]: key, children: groupedData[key], notHide: true })
    })
    return res1
  }

  /**
   * 寻找路径
   * @param val 值
   * @param data 数据
   */
  static findPath(val: string, data: any[]) {
    if (data.length === 0) {
      return val
    }
    let find = false
    let paths = []
    let count = 0
    while (!find) {
      count++
      if (count > 50) {
        break
      }
      for (let datum of data) {
        if (val == datum.id) {
          if (!datum.parentId) {
            find = true
          }
          paths.push(datum.codeLable)
          val = datum.parentId
          break
        }
      }
    }
    if (paths.length > 0) {
      paths = paths.reverse()
      paths.splice(0, 1)
      return paths.join('/')
    }
    return ''
  }

  /**
   * 设置树形数据节点disable=true
   * @param data 树形数据
   * @param code 禁用的节点code
   * @param keyField key字段名
   * @param childrenField 子节点字段名
   */
  static setTreeDisabled(data: any, code: any, keyField: string = 'id', childrenField: string = 'children') {
    if (data && data.length > 0) {
      data.forEach((item: any) => {
        setTreeItemDisabled(item, code, keyField, childrenField)
      })
    }
  }

  /**
   * 搜索菜单项并返回匹配的键值和展开的键值
   * @param menuOptions 菜单选项
   * @param searchValue 搜索值
   * @returns {{matchedKeys: string[], expandedKeys: string[]}} 匹配的键值和需要展开的键值
   */
  static searchMenuOptions(
    menuOptions: MenuOption[],
    searchValue: string
  ): { matchedKeys: string[]; expandedKeys: string[] } {
    const matchedKeys: string[] = []
    const expandedKeys = new Set<string>()

    const searchInOption = (option: MenuOption, parentKeys: string[] = []) => {
      // 检查当前节点是否匹配
      const labelText = (typeof option.label === 'function' ? option.name || '' : String(option.label || '')) as string

      const nameText = String(option.name || '')

      const isMatch =
        labelText.toLowerCase().includes(searchValue.toLowerCase()) ||
        nameText.toLowerCase().includes(searchValue.toLowerCase())

      if (isMatch) {
        matchedKeys.push(option.key as string)
        // 添加所有父节点到展开键值中
        parentKeys.forEach(key => expandedKeys.add(key))
      }

      // 递归搜索子节点
      if (option.children) {
        option.children.forEach(child => {
          searchInOption(child, [...parentKeys, option.key as string])
        })
      }
    }

    menuOptions.forEach(option => searchInOption(option))

    return {
      matchedKeys,
      expandedKeys: Array.from(expandedKeys),
    }
  }

  /**
   * 传输文件时将请求体转成FormData  非文件信息用dtoJson接收
   * @param params
   */
  static paramsToFormData(params: any) {
    let formData = new FormData()
    let otherParam: any = {}
    Object.keys(params).forEach(key => {
      if (Array.isArray(params[key])) {
        for (const k of toRaw(params[key])) {
          if (k instanceof File) {
            formData.append(key, k)
          }
        }
        if (!formData.has(key)) {
          otherParam[key] = params[key]
        }
      } else {
        if (params[key] instanceof File) {
          formData.append(key, params[key])
        } else {
          otherParam[key] = params[key]
        }
      }
    })
    formData.append('dtoJson', JSON.stringify(otherParam))
    return formData
  }

  /**
   * 获取两个日期字符串间的所有日期字符串
   * @param startDateStr
   * @param endDateStr
   */
  static getDateStrsBetweenDateArea(startDateStr: string, endDateStr: string, formatStr: string) {
    let dates: string[] = []
    if (!startDateStr || !endDateStr || !formatStr) {
      return dates
    }
    let startDate = new Date(startDateStr)
    const endDate = new Date(endDateStr)

    while (startDate <= endDate) {
      dates.push(this.formatDate(startDate, formatStr))
      startDate.setDate(startDate.getDate() + 1)
    }
    return dates
  }

  private static formatDate(date: Date, formatStr: string): string {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return formatStr.replace('YYYY', year.toString()).replace('MM', month).replace('DD', day)
  }

  static formatDateToStr(date: Date, formatStr: string = 'YYYY-MM-DD HH:mm:ss'): string {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')

    return formatStr
      .replace('YYYY', year.toString())
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds)
  }

  /**
   * 拉平树形结构
   */
  static flattenTree(tree) {
    const result = []
    if (!tree || !tree.length) {
      return []
    }

    function traverse(node) {
      if (node) {
        result.push(node)
        if (node.children && node.children.length > 0) {
          node.children.forEach(child => traverse(child))
        }
      }
    }

    tree.forEach(node => traverse(node))
    return result
  }

  /**
   * 判断设备是否为移动设备
   * @returns {boolean} 设备是否为移动设备
   */
  static isMobile(): boolean {
    const userAgentInfo = navigator.userAgent
    const Agents = ['Android', 'iPhone', 'SymbianOS', 'Windows Phone', 'iPad', 'iPod']
    let flag = false
    for (let v = 0; v < Agents.length; v++) {
      if (userAgentInfo.indexOf(Agents[v]) > 0) {
        flag = true
      }
    }
    return flag
  }

  /**
   * 获取文件MD5
   * @param file 文件对象
   * @returns 返回文件的MD5值
   */
  static getfileMd5(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const bmf = new BMF()
      bmf.md5(
        file,
        (err, md5) => {
          if (err) {
            console.log('err:', err)
            reject(err)
          } else {
            console.log('md5:', md5)
            resolve(md5)
          }
        },
        progress => {
          // 可以在这里处理进度信息
          console.log('MD5计算进度:', progress)
        }
      )
    })
  }

  static getAssetsFile(url: string) {
    return new URL(`../assets/images/gateway/${url}`, import.meta.url).href
  }
  /**
   * 判断一个日期是否在时间段内
   * @param startTime 开始时间
   * @param value  待校验时间
   * @param endTime 结束时间
   */
  static isDateInRange(
    startTime: string | number | Date,
    value: string | number | Date,
    endTime: string | number | Date
  ): boolean {
    // 将字符串转换为日期对象
    const valueDate = new Date(value)

    // 如果没有开始时间 或 value
    if (!startTime || !value) {
      return false
    }

    const startDate = new Date(startTime)
    const endDate = endTime ? new Date(endTime) : null

    // 判断条件
    if (endDate) {
      return startDate <= valueDate && valueDate <= endDate
    } else {
      return valueDate >= startDate
    }
  }
  static getCurrentMonth() {
    const date = new Date()
    const year = date.getFullYear() // 获取年份
    const month = String(date.getMonth() + 1).padStart(2, '0') // 获取月份并补零
    return `${year}-${month}` // 返回格式化的字符串
  }
  static getLastMonth() {
    const date = new Date()
    const currentMonth = date.getMonth()

    if (currentMonth === 0) {
      return `${date.getFullYear() - 1}-12`
    } else {
      const lastMonth = currentMonth - 1
      const year = date.getFullYear()

      return `${year}-${String(lastMonth + 1).padStart(2, '0')}`
    }
  }
  static async downloadFileAndConvert(url: string, filename: string) {
    try {
      const response = await fetch(url)

      if (!response.ok) {
        throw new Error('Network response was not ok')
      }

      // 将响应转换为 Blob
      const blob = await response.blob()

      // 创建 File 对象
      const file = new File([blob], filename, { type: blob.type })

      // 返回 File 对象
      return file
    } catch (error) {
      console.error('There was a problem with the fetch operation:', error)
    }
  }

  /**
   * 高亮文本中的搜索关键词
   * @param text 原始文本
   * @param keyword 搜索关键词
   * @returns 带有高亮标记的HTML
   */
  static highlightText(text: string, keyword: string): string {
    if (!keyword) return text
    const reg = new RegExp(keyword, 'gi')
    return text.replace(reg, match => `<span class="search-highlight">${match}</span>`)
  }

  /**
   * 将数字转换为中文数字
   * @param num 要转换的数字
   * @returns 中文数字字符串
   */
  static numberToChinese(num: number): string {
    const chineseNums = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
    const numStr = num.toString()
    let result = ''
    for (let i = 0; i < numStr.length; i++) {
      const digit = parseInt(numStr[i])
      result += chineseNums[digit]
    }
    return result
  }

  /**
   * 获取菜单options
   * @param routes 路由
   * @param options 菜单
   */
  static getMenuOptions(routes: RouteRecordRaw[], options: MenuOption[] = []): MenuOption[] {
    routes.forEach((route: RouteRecordRaw) => {
      if (route.meta?.hide) {
        return
      }
      let option: MenuOption
      if (!route.path) {
        option = {
          label: route.meta?.displayName,
          key: route.name as any,
          icon: JPGlobal.renderIcon(FolderOpenOutlineIcon),
        }
      } else {
        option = {
          label: () =>
            h(
              RouterLink,
              {
                to: {
                  path: route.path,
                },
              },
              { default: () => route.meta?.displayName }
            ),
          name: route.meta?.displayName,
          key: route.path,
          icon: JPGlobal.renderIcon(BookIcon),
        }
      }
      option.systemId = route.meta?.systemId
      option.id = route.meta?.id
      option.parentId = route.meta?.parentId
      if (route.children && route.children.length > 0) {
        option.children = this.getMenuOptions(route.children)
      }
      options.push(option)
    })

    return options
  }

  /**
   * 将下划线命名转换为驼峰命名
   * @param str 下划线字符串
   * @returns 驼峰字符串
   */
  static underscoreToCamelCase(str: string): string {
    return str.replace(/_([a-z])/g, (match, letter) => letter.toUpperCase())
  }

  /**
   * 将驼峰命名转换为下划线命名
   * @param str 驼峰字符串
   * @returns 下划线字符串
   */
  static camelCaseToUnderscore(str: string): string {
    return str.replace(/([A-Z])/g, '_$1').toLowerCase()
  }

  /**
   * 批量转换对象属性名：下划线转驼峰
   * @param obj 要转换的对象
   * @returns 转换后的对象
   */
  static convertObjectKeysToCamelCase(obj: any): any {
    if (obj === null || typeof obj !== 'object') {
      return obj
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.convertObjectKeysToCamelCase(item))
    }

    const result: any = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const camelKey = this.underscoreToCamelCase(key)
        result[camelKey] = this.convertObjectKeysToCamelCase(obj[key])
      }
    }
    return result
  }

  /**
   * 批量转换对象属性名：驼峰转下划线
   * @param obj 要转换的对象
   * @returns 转换后的对象
   */
  static convertObjectKeysToUnderscore(obj: any): any {
    if (obj === null || typeof obj !== 'object') {
      return obj
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.convertObjectKeysToUnderscore(item))
    }

    const result: any = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const underscoreKey = this.camelCaseToUnderscore(key)
        result[underscoreKey] = this.convertObjectKeysToUnderscore(obj[key])
      }
    }
    return result
  }
}

/**
 * 设置树形数据当前节点及其子节点disable=true
 * @param item 节点
 * @param code 禁用的节点code
 * @param keyField key字段名
 * @param childrenField 子节点字段名
 * @param disabled 禁用状态(不用传)
 */
function setTreeItemDisabled(
  item: any,
  code: any,
  keyField: string = 'id',
  childrenField: string = 'children',
  disabled: boolean = false
) {
  if (!disabled) {
    disabled = item[keyField] == code
  }
  Reflect.set(item, 'disabled', disabled)
  if (item[childrenField] && item[childrenField].length > 0) {
    item[childrenField].forEach((tt: any) => {
      setTreeItemDisabled(tt, code, keyField, childrenField, disabled)
    })
  }
}

function clearAll(obj: any) {
  for (const key in obj) {
    if (Array.isArray(obj[key])) {
      obj[key] = []
    } else if (typeof obj[key] === 'object' && obj[key] !== null) {
      clearAll(obj[key]) // 递归调用清空子对象
    } else {
      obj[key] = null // 或者 obj[key] = undefined;
    }
  }
}

function genChildren(data: Array<any>, prop: Option) {
  let selection: Array<Option> = []
  for (let d of data) {
    let item: Option = {
      label: d[prop.label],
      value: d[prop.value],
    }
    selection.push(item)
    if (d.children) {
      item.children = genChildren(d.children!, prop)
    }
  }
  return selection
}

function getChildren(data: Array<any>, treeData: Array<any>, id: string | number, parentId: string, label: string) {
  treeData.forEach(parent => {
    parent.key = parent[id]
    if (label) {
      parent.label = parent[label]
    } else if (parent.orgName) {
      parent.label = parent.orgName
    }
    let children: any = []
    data.forEach(child => {
      if (child[parentId] == parent[id]) {
        children.push(child)
      }
    })
    parent.lowNum = 0
    if (children.length > 0) {
      parent.children = children
      parent.lowNum = children.length
      parent.children.sort((a: any, b: any) => {
        return a[id] - b[id]
      })
      getChildren(data, children, id, parentId, label)
    }
  })
  
}

export default JPGlobal
