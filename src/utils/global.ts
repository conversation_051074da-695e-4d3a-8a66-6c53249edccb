import { App, Component } from 'vue'
// 全局方法
import JPGlobal from '@/types/common/jglobal'

// 全局组件注册
import * as comps from '@/types/common/jcomponents'
import { im } from 'mathjs'

import AdaptiveDataTable from '@/components/common/crud/components/AdaptiveDataTable.vue'
const components: {
  [propName: string]: Component
} = {
  // 注册到此处
  'j-container': comps.Container, // 布局
  'j-title-line': comps.TitleLine, // 标题
  'j-crud': comps.CRUD, // 增删改查组件
  'j-n-data-table': AdaptiveDataTable, // 自适应表格
  'j-select': comps.Select, // 下拉选
  'j-context-menu': comps.ContextMenu, // 上下文菜单
  'j-hospital': comps.Hospital,
  'j-upload': comps.Upload, //文件上传
  'j-export': comps.Export, //Excel导出
  'j-flow': comps.Flow, // 流程图
  'j-org-flow': comps.OrgFlow, // 组织架构流程图
  'j-org': comps.Org, //组织架构
  'j-organization-flow': comps.OrganizationFlow, // 组织架构
  'j-text-edit': comps.TextEdit, // 文本编辑
  'j-icon': comps.Icon, // 图标
  'j-bpmn': comps.Bpmn, // bpmn
  'j-preview': comps.NPreview, // 预览
  'j-image': comps.Image, // 图片
  'j-column': comps.Column, // 表格列
  'j-action-form': comps.ActionForm, // 表单
  'j-bus-emp-search': comps.EmpSearch, // 员工搜索
  'j-bus-hos-org': comps.HosOrg, // 医院组织架构（人力资源）
  'j-bus-asset-search': comps.AssetSearch, // 资产搜索
  'j-modal': comps.Modal, // 模态框
  'j-audit-flow': comps.AuditFlow, // 审核流
  'j-sign': comps.Sign, // 签名
  'j-search-title': comps.SearchTitle, // 查询头
  'j-form-item': comps.FormItem, // 表单
  'j-bus-audit-select': comps.AuditSelect, //审核流程选择
  'j-bus-audit-progress': comps.AuditProgress, // 审核进度
  'j-bus-audit-execution': comps.AuditExecution, // 审核执行
  'j-bus-audit-steps': comps.AuditSteps, // 审核步骤
  'j-bus-addr': comps.Addr, // 地址组件
  'j-bus-invoice-identify': comps.InvoiceIdentify, // 发票识别
  'j-upload-preview': comps.UploadPreview, // 上传预览
  'j-financial-account': comps.FinancialAccount, // 财务科目
  //bpm
  ContentWrap: comps.ContentWrap,
  'dict-tag': comps.DictTag,
}

// 注册全局设置
export function registerGlobalSettings(app: App<Element>) {
  // 全局自定义属性注册(需要和vue.d.ts声明文件对应)
  app.config.globalProperties.$jglobal = JPGlobal.getInstance()

  // 全局组件注册
  for (const comp in components) {
    app.component(comp, components[comp])
  }
}
