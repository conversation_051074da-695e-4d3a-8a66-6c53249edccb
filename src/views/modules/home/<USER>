<template>
  <div class="mobile-app-menu">
    <!-- 系统信息头部 - 压缩高度 -->
    <div class="system-header">
      <div class="system-info">
        <img :src="getAssetsFile(`${systemIcon}.png`)" class="system-avatar" />
        <div class="system-details">
          <h1 class="system-name">{{ systemName }}</h1>
          <p class="system-desc">{{ getSystemDescription() }}</p>
        </div>
      </div>
    </div>

    <!-- 搜索栏 - 压缩间距 -->
    <div class="search-section">
      <n-input v-model:value="searchValue" placeholder="搜索功能..." size="medium" clearable class="search-input">
        <template #prefix>
          <n-icon size="18"><SearchOutline /></n-icon>
        </template>
      </n-input>
    </div>

    <!-- PIN功能 - 置顶显示 -->
    <div class="pin-actions" v-if="pinMenus.length > 0 && !searchValue">
      <div class="section-title">
        <h2>📌 PIN功能</h2>
        <span class="section-subtitle">{{ pinMenus.length }}个</span>
      </div>
      <div class="pin-grid">
        <div v-for="(item, index) in pinMenus" :key="`pin-${index}`" class="pin-item" @click="handleMenuClick(item)">
          <n-badge :value="getMenuWarnNum(item)" :show-zero="false">
            <div class="pin-content">
              <div class="pin-icon">
                <n-icon size="22">
                  <component :is="getMenuIcon(item)" />
                </n-icon>
              </div>
              <span class="pin-title">{{ getMenuTitle(item) }}</span>
              <div class="pin-indicator">
                <n-icon size="12" color="#f59e0b">
                  <StarOutline />
                </n-icon>
              </div>
            </div>
          </n-badge>
        </div>
      </div>
    </div>

    <!-- 快捷功能 - 优化布局 -->
    <div class="quick-actions" v-if="quickMenus.length > 0 && !searchValue">
      <div class="section-title">
        <h2>常用功能</h2>
        <span class="section-subtitle">{{ quickMenus.length }}个</span>
      </div>
      <div class="quick-grid">
        <div v-for="(item, index) in quickMenus" :key="index" class="quick-item" @click="handleMenuClick(item)">
          <n-badge :value="getMenuWarnNum(item)" :show-zero="false">
            <div class="quick-content">
              <div class="quick-icon">
                <n-icon size="20">
                  <component :is="getMenuIcon(item)" />
                </n-icon>
              </div>
              <span class="quick-title">{{ getMenuTitle(item) }}</span>
            </div>
          </n-badge>
        </div>
      </div>
    </div>

    <!-- 功能分类 - 改进网格布局 -->
    <div class="menu-categories">
      <div v-for="(category, categoryIndex) in filteredMenuCategories" :key="categoryIndex" class="category-section">
        <div class="section-title">
          <h2>{{ category.title }}</h2>
          <span class="section-subtitle">{{ category.items.length }}个</span>
        </div>

        <!-- 优化菜单布局 - 重要功能占满一行，普通功能3列网格 -->
        <div class="menu-layout">
          <!-- 重要功能（有子菜单的功能）- 占满一行显示更多内容 -->
          <div v-if="category.importantItems.length > 0" class="important-section">
            <div
              v-for="(item, itemIndex) in category.importantItems"
              :key="`important-${itemIndex}`"
              class="menu-card important-card full-width"
              @click="handleMenuClick(item)"
            >
              <n-badge :value="getMenuWarnNum(item)" :show-zero="false" class="card-badge">
                <div class="card-content">
                  <div class="card-header">
                    <div class="card-icon">
                      <n-icon size="22">
                        <component :is="getMenuIcon(item)" />
                      </n-icon>
                    </div>
                    <div v-if="hasChildren(item)" class="submenu-indicator">
                      <n-icon size="12"><ChevronForwardOutline /></n-icon>
                    </div>
                  </div>
                  <div class="card-body">
                    <h3 class="card-title">{{ getMenuTitle(item) }}</h3>
                    <p v-if="hasChildren(item)" class="card-subtitle">{{ getChildrenCount(item) }}个子功能</p>

                    <!-- 子菜单预览标签 - 支持直接点击，显示子菜单提示数字 -->
                    <div v-if="hasChildren(item)" class="card-preview">
                      <div class="preview-tags">
                        <span
                          v-for="(child, childIndex) in item.children.slice(0, 4)"
                          :key="childIndex"
                          class="preview-tag clickable-tag"
                          @click.stop="handlePreviewTagClick(child)"
                        >
                          <n-badge
                            :value="getMenuWarnNum(child)"
                            :show-zero="false"
                            :offset="[0, -7]"
                          >
                            <span>{{ getMenuTitle(child) }}</span>
                          </n-badge>
                        </span>
                        <span
                          v-if="item.children.length > 4"
                          class="preview-more clickable-tag"
                          @click.stop="handleMenuClick(item)"
                        >
                          +{{ item.children.length - 4 }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </n-badge>
            </div>
          </div>

          <!-- 普通功能（叶子节点）- 3列网格布局 -->
          <div v-if="category.normalCards.length > 0" class="normal-section">
            <div class="menu-grid">
              <div
                v-for="(item, itemIndex) in category.normalCards"
                :key="`normal-${itemIndex}`"
                class="menu-card normal-card"
                @click="handleMenuClick(item)"
              >
                <n-badge :value="getMenuWarnNum(item)" :show-zero="false" class="card-badge">
                  <div class="card-content">
                    <div class="card-icon">
                      <n-icon size="20">
                        <component :is="getMenuIcon(item)" />
                      </n-icon>
                    </div>
                    <h3 class="card-title">{{ getMenuTitle(item) }}</h3>
                  </div>
                </n-badge>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- iOS风格子菜单模态框 -->
    <n-modal
      v-model:show="showSubmenuModal"
      preset="card"
      :title="currentSubmenu?.title"
      :bordered="false"
      :closable="true"
      class="submenu-modal"
      :style="submenuModalStyle"
    >
      <div class="submenu-container">
        <div class="submenu-grid">
          <div
            v-for="(subItem, subIndex) in currentSubmenu?.children"
            :key="subIndex"
            class="submenu-item"
            @click="handleSubmenuClick(subItem)"
          >
            <n-badge :value="getMenuWarnNum(subItem)" :show-zero="false">
              <div class="submenu-content">
                <div class="submenu-icon">
                  <n-icon size="24">
                    <component :is="getMenuIcon(subItem)" />
                  </n-icon>
                </div>
                <span class="submenu-title">{{ getMenuTitle(subItem) }}</span>
                <div v-if="hasChildren(subItem)" class="submenu-arrow">
                  <n-icon size="10"><ChevronForwardOutline /></n-icon>
                </div>
              </div>
            </n-badge>
          </div>
        </div>

        <!-- 三级菜单提示 -->
        <div v-if="hasThirdLevelMenus" class="todo-section">
          <n-alert type="info" :show-icon="false" class="todo-alert">
            <div class="todo-content">
              <strong>开发提醒</strong>
              <p>三级菜单正在开发中...</p>
            </div>
          </n-alert>
        </div>
      </div>
    </n-modal>

  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, watch } from 'vue'
  import { useRouter } from 'vue-router'
  import { NInput, NIcon, NBadge, NModal, NAlert } from 'naive-ui'
  import {
    SearchOutline,
    ChevronForwardOutline,
    GridOutline,
    DocumentTextOutline,
    SettingsOutline,
    PeopleOutline,
    BarChartOutline,
    CubeOutline,
    CardOutline,
    CalculatorOutline,
    ClipboardOutline,
    HomeOutline,
    StatsChartOutline,
    FlowerOutline,
    StarOutline,
  } from '@vicons/ionicons5'
  import { useSysStore } from '@/store'
  import { useFastArriveStore } from '@/store/fastArrive'
  import { useUserFavoriteMenuStore } from '@/store/userFavoriteMenu'
  import { getAssetsFile } from '@/utils'

  // 路由和状态管理
  const router = useRouter()
  const sysStore = useSysStore()
  const arriveStore = useFastArriveStore()
  const favoriteMenuStore = useUserFavoriteMenuStore()

  // 响应式数据
  const searchValue = ref('')
  const showSubmenuModal = ref(false)
  const currentSubmenu = ref<any>(null)

  // 计算属性
  const systemName = computed(() => sysStore.getSystemInfo.systemName || 'HRP系统')
  const systemIcon = computed(() => getSystemIcon(sysStore.getSystemInfo.systemId))
  const menuOptions = computed(() => sysStore.getRoutes)

  // 模态框样式
  const submenuModalStyle = computed(() => ({
    width: '90%',
    maxWidth: '400px',
    maxHeight: '70vh',
    borderRadius: '16px',
  }))

  // 获取系统描述
  const getSystemDescription = () => {
    const descriptions: Record<number, string> = {
      1: '系统核心管理平台',
      2: '人力资源管理系统',
      4: '绩效管理系统',
      5: '采购管理系统',
      6: '物资管理系统',
      11: '资产管理系统',
      16: 'OA办公系统',
    }
    return descriptions[sysStore.getSystemInfo.systemId] || '综合管理平台'
  }

  // 获取系统图标
  const getSystemIcon = (systemId: number) => {
    const iconMap: Record<number, string> = {
      1: 'sys-core',
      2: 'sys-hrm',
      4: 'sys-pms',
      5: 'sys-purms',
      6: 'sys-mmis',
      11: 'sys-ams',
      16: 'sys-oa',
    }
    return iconMap[systemId] || 'sys-core'
  }

  // 获取菜单图标
  const getMenuIcon = (item: any) => {
    const menuName = getMenuTitle(item).toLowerCase()

    // 智能图标匹配
    if (menuName.includes('首页') || menuName.includes('主页')) return HomeOutline
    if (menuName.includes('设置') || menuName.includes('配置')) return SettingsOutline
    if (menuName.includes('人员') || menuName.includes('用户')) return PeopleOutline
    if (menuName.includes('报表') || menuName.includes('统计')) return StatsChartOutline
    if (menuName.includes('图表') || menuName.includes('分析')) return BarChartOutline
    if (menuName.includes('文档') || menuName.includes('档案')) return DocumentTextOutline
    if (menuName.includes('计算') || menuName.includes('核算')) return CalculatorOutline
    if (menuName.includes('清单') || menuName.includes('列表')) return ClipboardOutline
    if (menuName.includes('卡片') || menuName.includes('证件')) return CardOutline
    if (menuName.includes('库存') || menuName.includes('仓库')) return CubeOutline
    if (menuName.includes('流程')) return FlowerOutline

    return GridOutline
  }

  // 获取菜单标题
  const getMenuTitle = (item: any) => {
    return item.meta?.displayName || item.name || '未命名菜单'
  }

  // 获取菜单警告数量 - 集成arriveStore
  const getMenuWarnNum = (item: any) => {
    // 首先检查item本身是否有warnNum
    if (item.warnNum) {
      return typeof item.warnNum === 'function' ? item.warnNum() : item.warnNum
    }

    // 从arriveStore中查找对应的警告数量
    const arrive = arriveStore.arriveList.find(a => a.path === item.path || a.path === item.key)
    if (arrive) {
      return Number(arrive.warnNum || 0)
    }

    // 如果有子菜单，计算子菜单的总警告数量
    if (item.children && item.children.length > 0) {
      return item.children.reduce((total: number, child: any) => {
        return total + getMenuWarnNum(child)
      }, 0)
    }

    return 0
  }

  // 检查是否有子菜单
  const hasChildren = (item: any) => {
    return item.children && item.children.length > 0
  }

  // 获取子菜单数量
  const getChildrenCount = (item: any) => {
    return item.children ? item.children.length : 0
  }

  // PIN功能 - 置顶显示的重要功能
  const pinMenus = computed(() => {
    if (favoriteMenuStore.pinnedMenus.length > 0) {
      return favoriteMenuStore.getTopPinnedMenus(4).map((pinned: any) => ({
        path: pinned.menuPath,
        name: pinned.menuName,
        meta: {
          displayName: pinned.menuName,
          icon: pinned.menuIcon
        },
        warnNum: pinned.warnNum || 0
      }))
    }
    return []
  })

  // 常用功能 - 优先显示用户自定义的常用功能
  const quickMenus = computed(() => {
    // 如果用户有自定义常用功能，优先显示
    if (favoriteMenuStore.favoriteMenus.length > 0) {
      return favoriteMenuStore.getTopFavoriteMenus(6).map((favorite: any) => ({
        path: favorite.menuPath,
        name: favorite.menuName,
        meta: {
          displayName: favorite.menuName,
          icon: favorite.menuIcon
        },
        warnNum: favorite.warnNum || 0
      }))
    }

    // 否则显示默认的前6个菜单
    const allMenus = extractAllMenus()
    return allMenus.slice(0, 6) // 改为6个，3x2布局
  })

  // 提取所有菜单项 - 修复重复添加问题
  const extractAllMenus = () => {
    const routes = menuOptions.value
    if (!routes || routes.length === 0) return []

    const allMenuItems: any[] = []
    const processedIds = new Set<string>()

    const extractMenuItems = (items: any[], level = 0) => {
      items.forEach(item => {
        if (item.meta?.hide) return

        // 避免重复添加同一个菜单项
        const itemId = item.path || item.name || JSON.stringify(item)
        if (processedIds.has(itemId)) return
        processedIds.add(itemId)

        if (item.children && item.children.length > 0) {
          // 只在第一层添加父菜单，避免重复
          if (level === 0) {
            allMenuItems.push(item)
          }
          // 递归处理子菜单，但不添加父菜单
          extractMenuItems(item.children, level + 1)
        } else if (item.path) {
          // 添加叶子节点
          allMenuItems.push(item)
        }
      })
    }

    routes.forEach(route => {
      if (route.children) {
        extractMenuItems(route.children, 0)
      } else if (route.path && !route.meta?.hide) {
        allMenuItems.push(route)
      }
    })

    return allMenuItems
  }

  // 处理菜单分类 - 优化排序，有子菜单的占满一行，空行放到最后
  const filteredMenuCategories = computed(() => {
    const allMenus = extractAllMenus()

    // 过滤搜索结果
    const filteredMenus = searchValue.value
      ? allMenus.filter(item => getMenuTitle(item).toLowerCase().includes(searchValue.value.toLowerCase()))
      : allMenus

    // 重要功能：有子菜单的父级菜单（占满一行显示更多内容）
    const importantItems = filteredMenus.filter(item => hasChildren(item))

    // 普通功能：无子菜单的叶子节点
    const leafItems = filteredMenus.filter(item => !hasChildren(item))

    // 对普通功能进行排序优化，确保能够整齐排列
    const normalCards = arrangeNormalCards(leafItems)

    return [
      {
        title: searchValue.value ? '搜索结果' : '功能模块',
        items: filteredMenus,
        importantItems,
        normalCards,
      },
    ]
  })

  // 优化普通卡片排列，将空行放到最后
  const arrangeNormalCards = (items: any[]) => {
    if (items.length === 0) return []

    // 对于移动端3列布局，我们保持原有顺序
    // 让最后一行自然显示剩余项，不强制填充空白
    // 这样可以避免强制排序带来的用户体验问题
    return items
  }

  // 检查是否有三级菜单
  const hasThirdLevelMenus = computed(() => {
    return currentSubmenu.value?.children?.some((child: any) => child.children && child.children.length > 0)
  })

  // 处理菜单点击
  const handleMenuClick = (item: any) => {
    if (hasChildren(item)) {
      currentSubmenu.value = {
        title: getMenuTitle(item),
        children: item.children,
      }
      showSubmenuModal.value = true
    } else if (item.path) {
      router.push(item.path)
    }
  }

  // 处理子菜单点击
  const handleSubmenuClick = (subItem: any) => {
    if (hasChildren(subItem)) {
      // TODO: 处理三级菜单
      window.$message?.info('三级菜单功能开发中...')
    } else if (subItem.path) {
      router.push(subItem.path)
      showSubmenuModal.value = false
    }
  }

  // 处理预览标签点击 - 阻止冒泡，直接跳转
  const handlePreviewTagClick = (child: any) => {
    if (hasChildren(child)) {
      // 如果子菜单还有子菜单，打开子菜单模态框
      currentSubmenu.value = {
        title: getMenuTitle(child),
        children: child.children,
      }
      showSubmenuModal.value = true
    } else if (child.path) {
      // 直接跳转到子菜单页面
      router.push(child.path)
    }
  }

  // 监听arriveStore变化，实时更新提示数量
  watch(
    () => arriveStore.arriveList,
    () => {
      // 强制更新UI以反映最新的警告数量
      // 这里可以添加额外的逻辑，比如更新特定菜单项的状态
    },
    { deep: true }
  )

  // 生命周期
  onMounted(async () => {
    // 初始化快速到达数据
    try {
      await arriveStore.getFastArrive()
      console.log('移动端菜单：快速到达数据初始化完成', arriveStore.arriveList.length)
    } catch (error) {
      console.error('移动端菜单：快速到达数据初始化失败', error)
    }

    // 初始化用户常用功能数据
    try {
      await Promise.all([
        favoriteMenuStore.fetchFavoriteMenus(),
        favoriteMenuStore.fetchPinnedMenus()
      ])
      console.log('移动端菜单：用户常用功能初始化完成', favoriteMenuStore.favoriteMenuCount)
      console.log('移动端菜单：用户PIN功能初始化完成', favoriteMenuStore.pinnedMenuCount)
    } catch (error) {
      console.error('移动端菜单：用户常用功能初始化失败', error)
    }
  })
</script>

<style scoped>
  @reference "tailwindcss";

  /* 系统头部 - 简洁企业级设计 */
  .system-header {
    @apply bg-green-600 text-white p-4 mb-3;
  }

  .system-info {
    @apply flex items-center gap-3;
  }

  .system-avatar {
    @apply w-12 h-12 rounded-lg bg-white/20 p-2;
  }

  .system-details {
    @apply flex-1;
  }

  .system-name {
    @apply text-lg font-semibold mb-1;
  }

  .system-desc {
    @apply text-green-100 text-sm;
  }

  /* 搜索栏 - 简洁设计 */
  .search-section {
    @apply px-3 mb-4;
  }

  .search-input {
    @apply w-full rounded-md;
  }

  /* PIN功能 - 置顶显示 */
  .pin-actions {
    @apply px-3 mb-4;
  }

  .pin-grid {
    @apply grid grid-cols-2 gap-2;
  }

  .pin-item {
    @apply bg-gradient-to-br from-yellow-50 to-orange-50 rounded-lg p-3 active:bg-yellow-100 transition-all duration-200 border-2 border-yellow-200 shadow-sm;
  }

  .pin-content {
    @apply flex flex-col items-center text-center relative;
  }

  .pin-icon {
    @apply text-orange-600 mb-1;
  }

  .pin-title {
    @apply text-xs font-semibold text-gray-900 line-clamp-2 leading-tight;
  }

  .pin-indicator {
    @apply absolute -top-1 -right-1;
  }

  /* 快捷功能 - 简洁卡片设计 */
  .quick-actions {
    @apply px-3 mb-4;
  }

  .section-title {
    @apply flex items-center justify-between mb-3;
  }

  .section-title h2 {
    @apply text-base font-medium text-gray-900;
  }

  .section-subtitle {
    @apply text-xs text-gray-500;
  }

  .quick-grid {
    @apply grid grid-cols-3 gap-2;
  }

  .quick-item {
    @apply bg-white rounded-lg p-3 active:bg-gray-50 transition-colors border border-gray-200;
  }

  .quick-content {
    @apply flex flex-col items-center text-center;
  }

  .quick-icon {
    @apply text-green-600 mb-1;
  }

  .quick-title {
    @apply text-xs font-medium text-gray-900 line-clamp-2 leading-tight;
  }

  /* 菜单分类 - 简洁设计 */
  .menu-categories {
    @apply px-3;
  }

  .category-section {
    @apply mb-6;
  }

  /* 菜单布局 - 优化排列 */
  .menu-layout {
    @apply space-y-3;
  }

  .important-section {
    @apply space-y-2;
  }

  .normal-section {
    @apply mt-3;
  }

  .menu-grid {
    @apply grid grid-cols-3 gap-2;
  }

  /* 菜单卡片 - 简洁企业级设计 */
  .menu-card {
    @apply bg-white rounded-lg active:bg-gray-50 transition-colors border border-gray-200;
  }

  .important-card {
    @apply p-3 min-h-24;
  }

  .full-width {
    @apply w-full;
  }

  .normal-card {
    @apply p-3 aspect-square;
  }

  .card-content {
    @apply h-full flex flex-col;
  }

  .card-header {
    @apply flex items-start justify-between mb-2;
  }

  .card-body {
    @apply flex-1;
  }

  .card-icon {
    @apply text-green-600;
  }

  .card-title {
    @apply text-sm font-medium text-gray-900 line-clamp-2 leading-tight;
  }

  .card-subtitle {
    @apply text-xs text-gray-500 mt-1;
  }

  /* 子菜单预览标签 - 简洁设计 */
  .card-preview {
    @apply mt-2;
  }

  .preview-tags {
    @apply flex flex-wrap gap-1.5;
  }

  .preview-tag {
    @apply text-sm bg-gray-100 px-3 py-1.5 rounded-md text-gray-700 flex items-center gap-1.5 transition-all duration-200;
    min-height: 48px;
  }

  .clickable-tag {
    @apply cursor-pointer hover:bg-gray-200 active:bg-gray-300 hover:shadow-sm;
  }

  .clickable-tag:hover {
    transform: translateY(-1px);
  }

  .clickable-tag:active {
    transform: translateY(0);
  }

  .preview-badge {
    @apply scale-90;
  }

  .preview-badge :deep(.n-badge-sup) {
    position: relative;
    transform: none;
    margin-left: 2px;
    font-size: 9px;
    min-width: 14px;
    height: 14px;
    line-height: 12px;
  }

  .preview-more {
    @apply text-sm bg-green-100 px-3 py-1.5 rounded-md text-green-700 font-medium transition-all duration-200;
    min-height: 28px;
  }

  .submenu-indicator {
    @apply absolute top-2 right-2 text-gray-400;
  }

  .card-badge {
    @apply w-full h-full;
  }

  /* 子菜单模态框 - 简洁设计 */
  .submenu-container {
    @apply p-3;
  }

  .submenu-grid {
    @apply grid grid-cols-3 gap-2 mb-4;
  }

  .submenu-item {
    @apply bg-white rounded-lg p-3 active:bg-gray-50 transition-colors border border-gray-200;
  }

  .submenu-content {
    @apply flex flex-col items-center text-center;
  }

  .submenu-icon {
    @apply text-green-600 mb-1;
  }

  .submenu-title {
    @apply text-xs font-medium text-gray-900 line-clamp-2 leading-tight;
  }

  .submenu-arrow {
    @apply absolute top-2 right-2 text-gray-400;
  }

  /* 开发提示 */
  .todo-section {
    @apply mt-4;
  }

  .todo-alert {
    @apply border-l-4 border-green-500;
  }

  .todo-content strong {
    @apply text-green-700 block;
  }

  .todo-content p {
    @apply text-gray-600 mt-1 text-xs;
  }

  /* 徽章样式 */
  :deep(.n-badge) {
    .n-badge-sup {
      @apply bg-red-500 border border-white;
      font-weight: 600;
      font-size: 10px;
      min-width: 16px;
      height: 16px;
      line-height: 14px;
      border-radius: 8px;
    }
  }

  /* 模态框样式 */
  :deep(.n-modal) {
    border-radius: 12px;
  }

  :deep(.n-card-header) {
    padding: 16px;
    font-weight: 600;
    font-size: 16px;
    border-bottom: 1px solid #e5e7eb;
  }

  :deep(.n-card__content) {
    padding: 16px;
  }

  /* 整体背景 */
  .mobile-app-menu {
    @apply min-h-screen pb-4 bg-gray-50 mb-10
    ;
  }
</style>
